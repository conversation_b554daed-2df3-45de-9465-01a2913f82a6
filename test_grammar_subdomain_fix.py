#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试验证修复后的grammar_subdomain模型评估指标计算
验证compute_metrics函数是否正确处理标签格式和索引
"""

import torch
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
from collections import Counter
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修复后的模型
from my_roberta_grammar_subdomainv1 import RoBERTaForConversationClassification

def create_mock_eval_pred():
    """
    创建模拟的评估预测数据，用于测试compute_metrics函数
    """
    batch_size = 3
    grammar_label_count = 4  # 假设有4个语法标签
    subdomain_label_count = 3  # 假设有3个子域标签
    
    # 模拟预测结果 (preds)
    preds = {
        'grammar_logits': torch.randn(batch_size, grammar_label_count),  # 语法分类logits
        'subdomain_logits': torch.randn(batch_size, subdomain_label_count)  # 子域分类logits
    }
    
    # 模拟真实标签 (labels) - 列表格式，模拟HuggingFace trainer的输出
    
    # Grammar标签 - multi-hot编码
    grammar_labels = torch.zeros(batch_size, grammar_label_count)
    grammar_labels[0, [0, 2]] = 1  # 第一个样本激活标签0和2
    grammar_labels[1, [1]] = 1     # 第二个样本激活标签1
    grammar_labels[2, [0, 1, 3]] = 1  # 第三个样本激活标签0,1,3
    
    # Subdomain标签 - 单标签
    subdomain_labels = torch.tensor([0, 1, 2])  # 0-based索引
    
    # 按照compute_metrics期望的格式组织labels
    labels = [
        grammar_labels,
        subdomain_labels
    ]
    
    return (preds, labels)

def test_compute_metrics():
    """
    测试修复后的compute_metrics函数
    """
    print("🧪 开始测试修复后的compute_metrics函数...")
    
    # 创建模拟数据
    eval_pred = create_mock_eval_pred()
    preds, labels = eval_pred
    
    print(f"\n📊 测试数据概览:")
    print(f"  - 预测数据keys: {list(preds.keys())}")
    print(f"  - 标签数据长度: {len(labels)}")
    print(f"  - grammar_labels类型: {type(labels[0])}")
    print(f"  - subdomain_labels类型: {type(labels[1])}")
    print(f"  - grammar_labels形状: {labels[0].shape}")
    print(f"  - subdomain_labels形状: {labels[1].shape}")
    print(f"  - grammar_labels样例: {labels[0]}")
    print(f"  - subdomain_labels样例: {labels[1]}")
    
    # 测试compute_metrics函数
    try:
        print(f"\n🔧 调用compute_metrics函数...")
        metrics = RoBERTaForConversationClassification.compute_metrics(eval_pred, debug=True)
        
        print(f"\n✅ 测试成功! 返回的指标:")
        for metric_name, metric_value in metrics.items():
            print(f"  - {metric_name}: {metric_value:.4f}")
            
        # 验证指标是否合理
        assert 'grammar_f1' in metrics, "缺少grammar_f1指标"
        assert 'subdomain_acc' in metrics, "缺少subdomain_acc指标"
        
        assert 0 <= metrics['grammar_f1'] <= 1, f"grammar_f1超出范围: {metrics['grammar_f1']}"
        assert 0 <= metrics['subdomain_acc'] <= 1, f"subdomain_acc超出范围: {metrics['subdomain_acc']}"
        
        # 检查指标是否合理
        if metrics['grammar_f1'] > 0.01:
            print(f"✅ grammar_f1计算正常: {metrics['grammar_f1']:.4f}")
        else:
            print(f"⚠️ grammar_f1较低: {metrics['grammar_f1']:.4f} (可能是数据问题)")
            
        if metrics['subdomain_acc'] > 0.01:
            print(f"✅ subdomain_acc计算正常: {metrics['subdomain_acc']:.4f}")
        else:
            print(f"⚠️ subdomain_acc较低: {metrics['subdomain_acc']:.4f} (可能是数据问题)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_label_consistency():
    """
    测试标签索引一致性
    """
    print(f"\n🔍 测试标签索引一致性...")
    
    # 加载标签映射
    try:
        grammar_map, _ = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_grammar_labels.json")
        subdomain_map, _ = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_subdomain_labels.json")
        
        print(f"✅ 标签映射加载成功")
        print(f"  - 语法标签数量: {len(grammar_map)}")
        print(f"  - 子域标签数量: {len(subdomain_map)}")
        
        # 检查标签值范围
        grammar_values = list(grammar_map.values())
        subdomain_values = list(subdomain_map.values())
        
        print(f"  - 语法标签值范围: [{min(grammar_values)}, {max(grammar_values)}]")
        print(f"  - 子域标签值范围: [{min(subdomain_values)}, {max(subdomain_values)}]")
        
        # 验证是否是连续的0-based索引
        expected_grammar_values = list(range(len(grammar_map)))
        expected_subdomain_values = list(range(len(subdomain_map)))
        
        if sorted(grammar_values) == expected_grammar_values:
            print(f"✅ 语法标签索引一致性检查通过 (0-based连续)")
        else:
            print(f"❌ 语法标签索引不一致: 期望{expected_grammar_values}, 实际{sorted(grammar_values)}")
            
        if sorted(subdomain_values) == expected_subdomain_values:
            print(f"✅ 子域标签索引一致性检查通过 (0-based连续)")
        else:
            print(f"❌ 子域标签索引不一致: 期望{expected_subdomain_values}, 实际{sorted(subdomain_values)}")
            
        return True
        
    except Exception as e:
        print(f"❌ 标签一致性测试失败: {e}")
        return False

def test_preprocess_consistency():
    """
    测试preprocess_dataset的标签处理一致性
    """
    print(f"\n🔧 测试preprocess_dataset标签处理...")
    
    try:
        # 创建模拟数据
        mock_dataset = {
            "sentence": ["这是一个测试句子", "另一个测试句子"],
            "grammar_labels": [["Query", "Reason"], ["Query"]],
            "subdomain_label": ["project", "general"]
        }
        
        # 加载标签映射
        grammar_map, _ = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_grammar_labels.json")
        subdomain_map, _ = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_subdomain_labels.json")
        
        print(f"  - 语法标签映射: {grammar_map}")
        print(f"  - 子域标签映射: {subdomain_map}")
        
        # 检查标签映射中是否包含测试标签
        test_grammar_labels = ["Query", "Reason"]
        test_subdomain_labels = ["project", "general"]
        
        for label in test_grammar_labels:
            if label in grammar_map:
                print(f"  - 语法标签 '{label}' -> {grammar_map[label]} (0-based)")
            else:
                print(f"  - ⚠️ 语法标签 '{label}' 不在映射中")
                
        for label in test_subdomain_labels:
            if label in subdomain_map:
                print(f"  - 子域标签 '{label}' -> {subdomain_map[label]} (0-based)")
            else:
                print(f"  - ⚠️ 子域标签 '{label}' 不在映射中")
        
        return True
        
    except Exception as e:
        print(f"❌ preprocess一致性测试失败: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("🚀 开始验证修复后的grammar_subdomain模型...")
    
    # 测试1: 标签索引一致性
    test1_passed = test_label_consistency()
    
    # 测试2: preprocess一致性
    test2_passed = test_preprocess_consistency()
    
    # 测试3: compute_metrics函数
    test3_passed = test_compute_metrics()
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"  - 标签索引一致性: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  - preprocess一致性: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"  - compute_metrics函数: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print(f"\n🎉 所有测试通过! 修复成功!")
        print(f"\n💡 建议:")
        print(f"  1. 现在可以重新训练模型，观察grammar_f1和subdomain_acc是否正常")
        print(f"  2. 在训练过程中关注验证指标的变化趋势")
        print(f"  3. 如果指标仍然异常，可能需要检查数据质量或模型架构")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修复代码")
    
    return test1_passed and test2_passed and test3_passed

if __name__ == "__main__":
    main()
