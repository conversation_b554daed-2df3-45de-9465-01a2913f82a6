[{"sentence": "项目P-25034574在什么地方？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574在哪儿？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-26734574在哪里？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574在哪个位置？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-19295574是什么位置？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574是哪个城市？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "位置，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "什么事项目P-92355574的位置？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "地点，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个事项目P-25035574的城市？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574当前什么味置？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25912874哪个第方？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574是在哪个城是？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "低点，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "xiangmu目P-25035574当前什么wei置？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25012374在哪个地fang？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25892374在na儿？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的基本信息是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的基本资料是什？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574多基础资料是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的简单信息是什？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "基础信息，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "什么是项目P-25035574的基本信息？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "基础资料，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个事项目P-25035574的基本资料？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的鸡本信息是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的基本字料是什？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574多基础资鸟是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "基粗信息，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的基本xinxi是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的基本信息是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的基础ziliao？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的组织架构是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-22935574的人员信息是什？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574都有哪些人？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-69785574有什么人组成？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25012974的组织形式？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25012974的组成人员？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "组织架构，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "什么是项目P-25035574的人员组成？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "人员信息，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "什么事项目P-25035574的人员结构？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "人圆信息，项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574都有哪些任？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的阻止架构是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25012974的组织行尸？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的zuzhi架构是什么？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-22935574的人员xinxi是什？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574都有哪些ren？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的组成renyuan？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14024501多项目夫这人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28024501的象目负责是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的项亩负责是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的项目负责任是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24124501的项目付这是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "xiang目P-24024501de项目负责人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的xiangmu负责人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的项目fuze人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "象目P-25025574的项目经是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25095574的项经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "想目P-25035374的负责人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25036574的管理者是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的老板是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的带头的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-25035574的项目经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁管P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁负责P-25033574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是谁做项目经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做负责人？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "管理P-25035537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "负责P-25035527的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "管P-25035527的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "guan理项目P-25035537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "fuze P-25035527的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的负责的到底是哪个混蛋？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的fuck负责的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的亿级项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的一集项目经理是啥？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的一级象目经理是心谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的一季象目经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的yi级项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的yiji项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的一级负责人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25036574的一级管理者是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的儿级项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的二集项目经理是啥？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的二级象目经理是心谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的二季象目经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的er级项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的erji项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的二级负责人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25036574的二级管理者是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的转职项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的赚职项目经理是啥？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的专职象目经理是心谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的专治象目经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的zhuan职项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的zhuanzhi项目经纪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的课户是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14345478的客沪是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-92345374的客胡是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58965374的kehu是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58945374的ke户是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-125645374的客hu是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-857645374的ke胡是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-15695678的client是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的最终用户是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的用户是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12349875的客户单位是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-25035574的客户？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做客户？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035537的帮谁建设的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-86253214的小售是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14345478的销手是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-92345374的销守是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58965374的xiaoshou是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58945374的xiao售是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-125645374的销shou是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-857645374的销sou是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-15695678的sales是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的销售人员是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的销售代表是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12349875的销售人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12349875的销售工程师是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12349875的xiaoshou售工程师是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12349875的销工程师是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25087537是谁签的合同？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁签订的项目P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售项目P-25035537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "xiao售项目P-25087537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25087537的合同是谁签的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-37290976的销售工程师？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "到底哪个是P-37290976的销售程师？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "到底哪个是P-37290976的销售？你清不清楚？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-37290976的销售是谁你摸清楚没有？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-37290976的xiaoshou是哪个你清楚末？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-86253214的一级小售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14345478的一级销手经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-92345374的亿级销守经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58965374的一级xiaoshou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58945374的一级xiao售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-125645374的yiji销shou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-857645374的yi级销sou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-15695678的一级sales manager是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的一级销售团队负责人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的一级销售管理者是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是项目P-25035574的一级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做一级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售项目P-25035537的一级销售经理是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "xiao售项目P-25087537的一级销售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-37290976的销售工程师的一级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "到底哪个是P-37290976的一级xiaoshou经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "到底哪个是P-37290976的一级销售经理？你清不清楚？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-37290976的一级销售jingli是谁你摸清楚没有？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-37290976的一级xiaoshou经理是哪个你清楚末？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-86253214的二级小售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14345478的二级销手经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-92345374的儿级销守经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58965374的二级xiaoshou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58945374的二级xiao售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-125645374的erji销shou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-857645374的er级销sou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-15695678的二级sales manager是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的二级销售团队负责人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12345678的二级销售管理者是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是项目P-25035574的二级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做二级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售项目P-25035537的二级销售经理是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "xiao售项目P-25087537的二级销售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-37290976的销售工程师的二级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "到底哪个是P-37290976的二级xiaoshou经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "到底哪个是P-37290976的二级销售经理？你清不清楚？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-37290976的二级销售jingli是谁你摸清楚没有？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-37290976的二级xiaoshou经理是哪个你清楚末？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-86252344的三级小售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14355478的三级销手经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-96545374的山级销守经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58978974的三级xiaoshou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-58945374的三级xiao售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-134565374的sanji销shou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-857645374的san级销sou经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-15678678的三级sales manager是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12283478的三级销售团队负责人是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-19812678的三级销售管理者是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是项目P-25035574的三级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做三级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售项目P-25035537的三级销售经理是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "xiao售项目P-25087537的三级销售经理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-37290976的销售工程师的三级销售经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "到底哪个是P-37290976的三级xiaoshou经理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "到底哪个是P-37290976的三级销售经理？你清不清楚？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-37290976的三级销售jingli是谁你摸清楚没有？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-37290976的三级xiaoshou经理是哪个你清楚末？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14024501多技术支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28024501的技漱支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的激素支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的技术支祠是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24124501的技术支此是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的jishu支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的技术支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的techincal support是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25025574的技术支是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25095574的技术持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的技支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的技术支持人员是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25036574的售前技术支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的售前是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的售前支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-25035574的技术支持？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁售前支持P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁技术支持P-25033574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是谁做技术支持？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做售前？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "售前P-25035537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "支持P-25035527的售前是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "管理P-25035527的售前是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "售前项目P-25035537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "shouqian P-25035527的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的售前支持的到底是哪个混蛋？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的fuck售前的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14024501多商务支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28024501的三物支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的商物支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的商务主持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24124501的商务支此是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的商务支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的商务zhichi是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的商wu支chi是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25025574的商务支是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25095574的商务持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的商支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的商务支持人员是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25036574的市场支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的商务人员是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的商务代表是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-25035574的商务支持？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁商务支持P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁支持P-25033574的商务部分？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是谁做商务支持？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做商务支持？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "支持P-25035537的商务的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "配合P-25035527的商务是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "协调P-25035527的商务是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "shang务支持项目P-25035537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "商务zhchi P-25035527的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的商务支持的到底是哪个混蛋？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的tm的商务支持的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-14024501多销售助理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28024501的销售组理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的消瘦助理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的销售助力是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24124501的销守助理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的xiaoshou助理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的销售zhuli是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的sales assistant是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25025574的销售助是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25095574的销助理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的售助理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售助理人员是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25036574的协助销售人员是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的销售支持人员是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的销售的帮手是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁是P-25035574的销售助理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁助理销售P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "谁技术支持P-25033574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是谁做销售助理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是谁做销售助理？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "助理销售P-25035537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035527的助理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "售卖P-25035527的助理是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "zhuli销售项目P-25035537的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "助理xiaoshou P-25035527的是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的销售助理的到底是哪个天杀的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的tmd的销售助理是边个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12356501多交付团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28087901的教父团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-23421501的交夫团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的交付团对是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的jiaofu团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的交付tuandui是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的交付team是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25025574的交付团是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98705574的交团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的付团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的调试团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-33446574的调试团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的交付团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的调试人员属于哪个团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的交付人员属于哪个小组？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个团队负责交付P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个团队负责调试P-25033574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是哪个团队做调试？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是哪个团队做交付？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "调试P-25035537的团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "交付P-25035527的团队是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "实施P-25035527的团队是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "tia<PERSON>项目P-25035537的团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "jiao付xiaoshou P-25035527的团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的调试团队的到底是哪个天杀的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的tmd的交付团队是边个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12356501多销售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28087901的销守团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-23421501的消售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的销售团对是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的xiaoshou团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的销售tuandui是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的销售team是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25025574的销售团是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98705574的销团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的售卖团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售小组是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售小队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售属于哪个团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售属于哪个小组？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个团队销售的P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个团队负责售卖P-25033574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是哪个团队做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是哪个小组做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035537的小组是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "售卖P-25035527的小组是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035527的人属于哪个团队是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的销售团队的到底是哪个谁是知道的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的tmd的销售团队是边个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12356501多亿级销售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28087901的一级销守团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-23421501的一级消售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的一级销售团对是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的一集xiaoshou团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的一集销售tuandui是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的亿级销售team是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25025574的一级销售团是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98705574的一级销团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的一级售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的一级售卖团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的一集销售小组是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的一集销售小队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售属于哪个一级团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售属于哪个一级小组？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个一级团队销售的P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个一级团队售卖的P-25033574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是哪个一级团队做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是哪个一级小组做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035537的一级小组是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "售卖P-25035527的一集小组是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035527的人属于哪个亿级团队是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的一级销售团队的到底是哪个谁是知道的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的tmd的一级销售团队是边个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12356501多二级销售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28087901的儿级销守团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-23421501的尔级消售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的耳级销售团对是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的二集xiaoshou团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的二集销售tuandui是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的二级销售team是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25025574的二级销售团是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98705574的二级销团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的二级售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的二级售卖团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的二集销售小组是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的二集销售小队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售属于哪个二级团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售属于哪个二级小组？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个二级团队销售的P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个尔级团队售卖的P-25033574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是哪个儿级团队做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是哪个二级小组做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035537的二级小组是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "售卖P-25035527的二集小组是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035527的人属于哪个二级团队是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的二级销售团队的到底是哪个谁是知道的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的tmd的二级销售团队是边个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12356501多三级销售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-28087901的三级销守团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-23421501的山级消售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24324501的三级销售团对是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的三集xiaoshou团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24044501的三集销售tuandui是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-24024501的山级销售team是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25025574的三级销售团是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98705574的三级销团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的三级售团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的三级售卖团队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的三集销售小组是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的三集销售小队是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售属于哪个三级团队？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035374的销售属于哪个三级小组？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个三级团队销售的P-25035574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个三级团队售卖的P-25033574？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25032557是哪个三级团队做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-2503557是哪个三级小组做销售？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035537的山级小组是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "售卖P-25035527的三集小组是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售P-25035527的人属于哪个三级团队是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-98292384的山级销售团队的到底是哪个谁是知道的？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-98292384的tmd的三级销售团队是边个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个业务分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个业务大类", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个业务类型", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个业务类别", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个业务明细类别", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "业务类别，项目P-25035574属于哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "业务类型，项目P-25035574属于哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的业务分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个野务分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个页务分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个业物分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "野务类别，项目P-25035574属于哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的yewu分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的业务fenlei？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的yewu类型？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个销售区域分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个销售分区大类", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个销售分区", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个销售区域类别", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个销售地区类别", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售区域类别，项目P-25035574属于哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "销售地区分类，项目P-25035574属于哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574销售的区域类型？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574销售的区域分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个销售区域分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个销售区与分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个销守区域分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "消售区域类别，项目P-25035574属于哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的销售quyu分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的销售区域fenlei？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的xiaoshou区域分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个分类？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个大类", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个类型？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于类别", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "类别，项目P-25035574属于哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的类型？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个分勒？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个类比？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574属于哪个累型？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "雷别，项目P-25035574属于哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的分lei？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的lei型？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是项目P-25035574的leibie？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574当前什么进度？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574进展到哪儿了？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574推进到什么程度了？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574到哪儿了？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "进度，项目P-25035574到哪儿了？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "什么事项目P-25035574的进展？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574当前什么进肚？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574仅展到哪里了？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574推进到什么成度？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "进肚，项目P-25035574到哪里了？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574当前什么jin度？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574进zhan到哪儿了？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574推进到什么chengdu了？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的名称是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的全名是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574叫什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的名字是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-25035574的全称是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "名字，项目P-25035572？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "哪个是P-25035574的全名？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目晶科能源的编码是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目晶科能源的编号是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目晶科能源的号码是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目晶科能源的code是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "晶科能源项目的code是什么", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "编号，项目晶科能源？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-34579807的物料123456789什么时候发货", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-34579807的物料859647823什么时候发", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-34579807的物料695874523什么时候运送", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-58963265的物料789256412什么时候快递", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-34579807的物料789256412什么时候顺丰", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-78965236的物liao 165832698什么时候快递", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-78956325的w<PERSON>ao 256987412什么时候发快递", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-34579807的物料123456789什么时候fahuo", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料789532641什么时候到现场", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-12569856的wu料659245896什么时候到场", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料598478529什么时候客户收到", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-89654125的wuliao 123598632什么时候到客户那", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-89635214的物料158942659何时到用户那", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料196854723哪天到现场？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料589642659何时到customer那", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-96358925的wu料158947895何时到yonghu那", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料158895659何时到kehu那", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料158942659何时到xianchang", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-74859632的服务器什么时候发货？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的服务器什么时候到现场？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料123456789在哪个箱子", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的Server在哪些箱子", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护在哪些屏柜上", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护在1#屏柜上吗", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的电脑在2#箱子中吗", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料123456789发货没有", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料123456789有没有到现场", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料123154789为什么没有发货", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料123456789为什么没有到现场", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料124563789为什么不在1号箱子里", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料123456789为什么没有快递", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料123859624为什么没有fahuo", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料589246789为什么么有fa", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的服务器为什么没有发货", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的服务器为什么没有发", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的服务器为什么没有fa", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的服务器为什么没有fahuo", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的服务器为什么没有快递", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的服务器为什么没有在2号箱子里", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC装置为什么没有发货", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC装置为什么不在3号箱子里", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC装置为什么不在3#箱子里", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC装置为什么不在3#box里", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC装置为什么不在3#箱子里", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC装置为什么么有到现场", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护为什么没有发货", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护为什么不在1#屏柜上", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护为什么不在1#Panel上", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护为什么不在1号Panel上", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护为什么不在2号Panel上", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护为什么不在4号Panel上", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-92840284的项目经理和售前技术支持都是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-12560284的交付和技术支持都是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-15969854的服务器和PMC保护都快递了没有？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-25690284的销售和技术支持都是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-36569854的售前技术支持和商务支持分别是哪个？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "售前技术支持和商务支持，项目P-23745893的，分别是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目经理和销售，项目P-22365893的，分别是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-92840284的manager和售前技术支持都是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-92840284的manager和sales都是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-34579807的物料123456789和服务器发货没有", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护和服务器在1#屏柜上吗", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的PMC保护和服务器在哪些屏柜上吗", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "<PERSON><PERSON><PERSON><PERSON>和商务支持，项目P-12345893的，分别是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目经理和销售，项目P-22365893的，分别是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目经理和sales，项目P-54675893的，分别是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "项目P-34579807的PMC保护和server在1#屏柜上吗", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "项目P-15969854的server和PMC保护都快递了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "项目P-15969854的屏柜和线缆都快递了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "项目P-15969854的项目经理和销售是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-23123854的项目经理和销售是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-15912354的售前技术支持和商务支持是谁？", "grammar_labels": ["query"], "subdomain_label": "baseinfo"}, {"sentence": "P-15969854的表记和PMC保护都顺风了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-15912354的server和PMC保护都顺风了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-21349854的线缆和PMC保护都顺风了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-12459854的panel和PMC保护都顺风了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-34535854的panel，PMC保护，服务器都顺风了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-12459854的屏柜，线缆，服务器都发货了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的软件，线缆，server都发货了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-13456854的仪表，线缆，服务器都发货了没有？", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "项目P-34345367的物料123456789和服务器为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的物料123455289和服务器为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-66457407的物料123145289和server为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的线缆和server为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-23423807的软件和server为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的保护和server为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-09239807的SEL保护和server为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34534567的施耐德保护和server为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-34579807的保护和联想服务器为什么没有发货？", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "项目P-83341123总共有多少面屏柜？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，可以帮忙查一下P-74456354这个项目规划了多少屏柜吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个G-20240518项目，屏柜数量是多少来着？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请求查询合同H-20231108下，项目P-99841355的屏柜总数。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "xiangmu P-77412354有多少面pinggui？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "给我个准数，P-20240077到底要多少屏柜。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦了，我想了解下P-10086A项目中屏柜的具体数量。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "统计一下P-9527B项目内的屏柜总数。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-20240319有哪些PMC设备，它们的规约分别是什么？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "您好，请问合同H-54188377里的PMC设备清单和对应的技术规约能提供吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-33445那个项目，PMC设备都有啥啊？啥规约的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请求拉取项目P-44556的PMC设备列表及其技术规约详情。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-55667有那些PMC设被？归约是啥？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "我想知道P-96547341项目都配置了哪些PMC设备，以及它们的具体规约。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "关于项目P-11223682，它的PMC设备清单和规约明细发我一下。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查一下P-88888365这个项目的PMC设备构成及规约等级。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-77889157有哪些物料还没完成细化？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦您帮我看一下，项目P-88990961还有哪些物料是需要细化的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-99001333这项目，是不是还有些东西没定下来？都有啥？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询项目P-12121777中，当前处于“未细化”状态的物料清单。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "向目P-23232222有哪些物料未细划？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "我想看看P-20240628这个项目里，哪些物料的状态还是“待明确”。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，请问P-31500122合同下有哪些物料没定稿？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "拉一下P-10010B33项目中所有未细化的物料列表。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，我想问一下“研华工控机”这个设备，货期大概是多长？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询一下H-20231108合同里那批服务器的供货周期。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个“CET保护”一般要等多久才能到货？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“录波仪”的huoqi是多久？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "告诉我一下“交换机”的采购周期是几天？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦查查，通常这种PLC得多长时间能供货？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“监控主机”的货期？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦您帮我确认下，H-20231108合同的屏柜是否已满足发运条件？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-74456354项目的屏柜能发货了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那批柜子，现在能走了不？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "确认一下P-88912001项目的自产屏柜齐套性，是否可以提报发运？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "项目P-74456354的平贵发货条件具备了吗？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "问一下，A03箱的屏柜发货条件齐备了没有？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "喂，P-88912001项目的屏柜物料都齐了没，能发了么？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，我想了解一下H-20240520合同的自产柜，里面的“研华工控机”到货了没？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "查询H-20240815合同下，屏柜内关键物料“CET保护”的到货状态。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个H-20231108合同的柜子，“保护装置”到了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-20231108合同的自产屏柜，CET baohu到了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "帮我看看合同H-20240520，它那个屏柜里的“交换机”是不是已经采购入库了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "合同H-20240815的自产柜，里面的东西都齐了？特别是那个“CET保护”。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-20231108的“自产屏柜”里，“CET保护”这个东西到了没？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请问，H-20240520合同的屏柜，它的核心部件“研华工控机”是否已到货？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“CET保护”发货了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "您好，请问P-74456354项目里的“研华工控机”发走了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那批“CET保护”，发了不？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询物料“研华工控机”在合同H-20231108下的出库发运状态。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“研华工控机”是否已发火？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "帮我确认下，发往张三的“CET保护”是否已经离厂。 ", "grammar_labels": ["query", "judge"], "subdomain_label": "delivery"}, {"sentence": "“研华工控机”发了没？ ", "grammar_labels": ["query", "judge"], "subdomain_label": "delivery"}, {"sentence": "P-74456354项目发货了没？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "你好，请问P-88912001项目发运情况如何，发了多少了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个P-74456354项目，走了多少货了？还有多少没走？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询项目P-88912001的发运状态和已发/未发物料计数。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "橡木P-74456354发货了吗？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "H-20231108这个合同发货进度怎么样？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查一下P-88912001发了没，顺便告诉我下未发数量。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“研华工控机”的发货申请单交了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦帮我查一下，“CET保护”的发货申请单提交了没有？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那台“研华工控机”，申请发货的单子弄了没？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请核实H-20231108合同下的“CET保护”是否已生成发货申请单。 ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "那个“研化工控机”的shenqingdan提交了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“CET保护”的发货申请流程走到哪了？是不是提交了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "我想知道“研华工控机”有没有提报发货。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354项目还有什么没发？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，可以帮我列一下H-20231108合同里还未发运的物料清单吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "还有啥东西没发走啊？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请求返回项目P-88912001的待发货物料明细。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "哪些shebei还没发？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查查P-74456354这个项目，未发货列表拉一下。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦了，我想看下H-20231108还欠哪些料没发。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“研华工控机”为什么还没发？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "请问一下，“CET保护”一直没发货是什么原因呢？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "那台“研华工控机”咋回事啊，卡哪了？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "请求溯源H-20231108合同下“CET保护”未发运的根本原因。 ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "那个“CET bao hu”为啥没法货？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "为什么“研华工控机”的发货延迟了？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "告诉我“CET保护”不发货的理由。 ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "H-20231108里的第三方设备是直发吗？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "你好，我想确认下，P-74456354项目里的外购件是厂家直接发运吗？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "那些不是咱们自己产的东西，是厂家直接寄给客户？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "核实合同H-20231108中，第三方物料的发运模式是否为供应商直送。 ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "那些di san fang设备是直发不？ ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的外购设备，发货方是谁？是原厂吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "第三方件的发货方式确认一下，是不是直发。 ", "grammar_labels": ["judge"], "subdomain_label": "delivery"}, {"sentence": "发走的“CET保护”型号对吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦核对一下，H-20231108发运的“研华工控机”型号和PMS系统里的是不是一样的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "我们发给张三那批货，型号有没有搞错？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请求比对合同H-20231108中已发运物料“CET保护”的物理型号与PMS系统记录是否存在差异或变更。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "发走的设备xinghao跟系统里一样吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查查H-20231108发运的“研华工控机”，型号有没有变过？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "已发货的“CET保护”，型号和系统有出入吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-20231108这批货的物流电话和运输方式？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，请问P-74456354项目的货物是怎么发的，承运方联系方式有吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "送货的电话多少？走的空运还是陆运？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询合同H-20231108的承运商联系信息及具体的发运模式。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "wuliu电话给下，怎么发的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的发运方式和物流公司电话是多少？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "喂，那批货的物流信息有吗？电话和运输方式。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“录波仪”在哪个箱子里？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请问，H-20231108合同的“录波仪”装箱位置是哪里？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦帮我定位一下，P-74456354项目里的“电脑”具体装在哪个包装箱内？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "我想找“电脑”，它被分到哪个箱了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那台“电脑”，在哪箱？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个luyiyi在几号箱？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "根据装箱清单，查询设备“录波仪”所属的箱号标识。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“研华工控机”是哪批发出的？箱号和清单发我。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-20231108合同的“研华工控机”，是哪天发的，在哪个箱子，里面还有啥？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "您好，我想查询一下“CET保护”这个物料所属的发货批次、装箱编号及详细清单。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "帮我找下“CET保护”的发货信息，包括批次、箱号和装箱内容。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那台“研华工控机”，跟哪批货一起走的？箱子编号和里面的东西有单子吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个“研华工控机”在哪一p货里？xiang hao和清单有吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请求关联查询设备“CET保护”的发运批次号、包装箱号及对应的装箱明细单。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354项目的“研华工控机”，谁收的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "您好，能帮我查一下合同H-20231108里那批“CET保护”的收件人信息吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那台“研华工控机”，寄给谁了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询合同H-20231108下，物料“CET保护”对应的指定接收人。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个“研华工控机”的shoujianren是谁？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“CET保护”的接收方联系人是谁？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦了，我想知道P-74456354项目那批货的收件人是哪位。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“CET保护”什么时候到的？谁签收的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦了，我想了解下“研华工控机”的预计到达时间和现场签收人。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那批货啥时候到的，谁拿的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请提供物料“CET保护”的交付时间戳及签收人记录。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "CET bao hu 啥时候到？谁签的字？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查下“研华工控机”的到货日期和签收详情。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，问下“CET保护”送到了没，谁签收了，什么时候签的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354项目的发货申请到财务那一步了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354项目发货申请到财务没？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦查下H-20231108这个合同的发货申请，财务审批通过了没？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "问一下，P-88912001的发货申请，提交给才务审批了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个P-88912001项目，发货申请cai wu shen pi了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-20231108合同的发货单，财务看了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354项目的发货申请是否已提报至财务节点？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的发货审批流到哪了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查查H-20231108的发货审批到哪儿了。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，我想知道H-20231108的发货流程当前节点。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请问P-88912001的发货流程目前在谁那？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个P-88912001项目的审批，现在卡在哪一步？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-20231108的发货shenpi，到哪个jiedian了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询P-74456354项目的发货审批流程当前所处环节。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的审批后面还有几步？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦告知一下H-20231108项目审批的剩余步骤。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请问H-20231108的流程走完还差哪些环节？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的审批liucheng，还剩啥没走完？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "审批流接下来是啥？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-20231108的审批，后面还有谁要审？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-88912001项目发货审批的后续流程节点有哪些？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的发货流程为啥不动了？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "H-20231108的发货流程怎么停了？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "H-20231108的发货审批卡在哪了，谁知道？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "查一下P-88912001的审批瓶颈在哪。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个P-88912001项目，怎么审了这么久？ ", "grammar_labels": ["reason"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的发货流程ka在哪了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请求定位H-20231108合同发货审批流程的阻塞节点。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354发货审批现在谁负责？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦了，我想知道P-88912001审批流的当前责任人。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请问H-20231108的审批人换了没？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-20231108审批负责人有变化吗？现在是谁在审？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个P-88912001的审批流，负责人还是张三吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-74456354的fuzeren是谁？换人了吗？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询合同H-20231108当前审批节点的处理人，并确认负责人是否有过变更。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-6632这份合同，新到的一批“服务器”大概要等多久？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，我想了解一下合同H-54321里那批“交换机”的供货周期。", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-6632那个合同的货期多长？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦查一下，合同H-9981采购的“工控机”需要多长的备货时间？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-6632合同最近的物料货期是多久？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询合同H-54321最新批次物料的预计交付周期。", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-9981合同的“PLC模块”得多长时间才能到？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“华为项目”的货是不是都发出去了？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "您好，我想确认一下“阿里项目”的所有设备是否已完成出库发运。", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“华为项目”还有东西没发吗？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“华为向木”的设备是不是都发完了？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查查“阿里项目”，是不是还有遗漏的没发。", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“华为项目”的发货状态是“全部已发”吗？", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“录波仪”装在几号箱了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦帮我查一下“交换机”的装箱编号。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那台“录波仪”，箱号是啥？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询设备“交换机”的包装箱号。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "luyiyi在哪个箱号？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "我想知道“录波仪”被装到哪个箱子里了。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "“交换机”的箱号？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374项目里的“服务器”具体装在哪个屏柜里了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请问，项目P-5543210的“汇控柜”是放在哪个位置的？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个P-19298374项目的“服务器”，在几号柜？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询项目P-5543210中“服务器”设备所在的物理屏柜编号。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "橡木P-19298374的“服务器”在哪个柜子上？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-5543210项目，它的“汇控柜”位置在哪？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "麻烦帮我找下，项目P-19298374的“服务器”搁在哪个柜子上了。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请问H-6632合同的发货流程当前在哪个环节？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "那个P-19298374的发货申请，现在谁在审？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询H-6632合同发货审批流程的当前状态节点。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374的发货审批到哪个节点了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-6632合同的发货流程走到哪一步了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "审批到哪了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374的发货申请到财务那了吗？ \t", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "你好，H-6632合同的发货流程，财务是不是已经审了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374那个单子，给财务看了没？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "核实H-6632合同的发货申请是否已流转至财务审批节点。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374项目的发货申请，财务审批过了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-6632合同的发货申请有没有提交到财务？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "财务审了没？就那个P-19298374项目的发货单。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请问H-6632合同的发货流程走完还差哪些环节？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374的审批，接下来是啥？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查询H-6632合同发货审批的后续流程节点。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374的审批，后面还要谁审？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "H-6632的审批流还剩啥没走完？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374那个项目，审批怎么停了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "请求定位H-6632合同发货审批流程的阻塞节点。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "P-19298374的发货流程卡在哪了？ ", "grammar_labels": ["query"], "subdomain_label": "delivery"}, {"sentence": "查查H-6632的审批瓶颈在哪一步。 ", "grammar_labels": ["query"], "subdomain_label": "delivery"}]