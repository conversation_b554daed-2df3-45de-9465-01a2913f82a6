#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试验证修复后的评估指标计算
验证compute_metrics函数是否正确处理标签格式和索引
"""

import torch
import numpy as np
from sklearn.metrics import f1_score
from collections import Counter
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修复后的模型
from my_roberta_entity_relationV2 import RoBERTaForConversationClassification

def create_mock_eval_pred():
    """
    创建模拟的评估预测数据，用于测试compute_metrics函数
    """
    batch_size = 2
    max_entities = 3
    max_relations = 2
    entity_label_count = 5
    relation_label_count = 3
    seq_len = 10
    
    # 模拟预测结果 (preds)
    preds = {
        'start_logits': torch.randn(batch_size, seq_len),  # 开始位置logits
        'end_logits': torch.randn(batch_size, seq_len),    # 结束位置logits
        'span_logits': torch.randn(batch_size, max_entities, entity_label_count),  # 实体类型logits
        'relation_logits': torch.randn(batch_size, max_relations, relation_label_count)  # 关系类型logits
    }
    
    # 模拟真实标签 (labels) - 列表格式，模拟HuggingFace trainer的输出
    entity_start_labels = torch.zeros(batch_size, seq_len)
    entity_end_labels = torch.zeros(batch_size, seq_len)
    
    # 设置一些实体边界
    entity_start_labels[0, 2] = 1  # 第一个句子，位置2是实体开始
    entity_end_labels[0, 4] = 1    # 第一个句子，位置4是实体结束
    entity_start_labels[1, 1] = 1  # 第二个句子，位置1是实体开始
    entity_end_labels[1, 3] = 1    # 第二个句子，位置3是实体结束
    
    # 模拟span_entity_labels (列表格式)
    span_entity_labels = [
        # 第一个句子的实体spans
        [[2, 4, 1], [5, 7, 2], [-1, -1, -1]],  # 2个实体 + 1个padding
        # 第二个句子的实体spans  
        [[1, 3, 0], [-1, -1, -1], [-1, -1, -1]]  # 1个实体 + 2个padding
    ]
    
    # 模拟entity_relation_labels (列表格式)
    entity_relation_labels = [
        # 第一个句子的关系
        [[2, 4, 5, 7, 1], [-1, -1, -1, -1, -1]],  # 1个关系 + 1个padding
        # 第二个句子的关系
        [[-1, -1, -1, -1, -1], [-1, -1, -1, -1, -1]]  # 2个padding
    ]
    
    # 按照compute_metrics期望的格式组织labels
    labels = [
        entity_start_labels,
        entity_end_labels, 
        span_entity_labels,
        entity_relation_labels
    ]
    
    return (preds, labels)

def test_compute_metrics():
    """
    测试修复后的compute_metrics函数
    """
    print("🧪 开始测试修复后的compute_metrics函数...")
    
    # 创建模拟数据
    eval_pred = create_mock_eval_pred()
    preds, labels = eval_pred
    
    print(f"\n📊 测试数据概览:")
    print(f"  - 预测数据keys: {list(preds.keys())}")
    print(f"  - 标签数据长度: {len(labels)}")
    print(f"  - span_entity_labels类型: {type(labels[2])}")
    print(f"  - entity_relation_labels类型: {type(labels[3])}")
    print(f"  - span_entity_labels样例: {labels[2]}")
    print(f"  - entity_relation_labels样例: {labels[3]}")
    
    # 测试compute_metrics函数
    try:
        print(f"\n🔧 调用compute_metrics函数...")
        metrics = RoBERTaForConversationClassification.compute_metrics(eval_pred, debug=True)
        
        print(f"\n✅ 测试成功! 返回的指标:")
        for metric_name, metric_value in metrics.items():
            print(f"  - {metric_name}: {metric_value:.4f}")
            
        # 验证指标是否合理
        assert 'start_end_f1' in metrics, "缺少start_end_f1指标"
        assert 'span_f1' in metrics, "缺少span_f1指标"
        assert 'relation_f1' in metrics, "缺少relation_f1指标"
        
        assert 0 <= metrics['start_end_f1'] <= 1, f"start_end_f1超出范围: {metrics['start_end_f1']}"
        assert 0 <= metrics['span_f1'] <= 1, f"span_f1超出范围: {metrics['span_f1']}"
        assert 0 <= metrics['relation_f1'] <= 1, f"relation_f1超出范围: {metrics['relation_f1']}"
        
        # 检查span_f1和relation_f1是否不再是接近0的虚假值
        if metrics['span_f1'] > 0.01:
            print(f"✅ span_f1修复成功: {metrics['span_f1']:.4f} (不再是虚假的接近0值)")
        else:
            print(f"⚠️ span_f1仍然很低: {metrics['span_f1']:.4f} (可能是数据问题)")
            
        if metrics['relation_f1'] > 0.01:
            print(f"✅ relation_f1修复成功: {metrics['relation_f1']:.4f} (不再是虚假的接近0值)")
        else:
            print(f"⚠️ relation_f1仍然很低: {metrics['relation_f1']:.4f} (可能是数据问题)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_label_consistency():
    """
    测试标签索引一致性
    """
    print(f"\n🔍 测试标签索引一致性...")
    
    # 加载标签映射
    try:
        entity_map, _ = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_entity_labels.json")
        relation_map, _ = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_relation_labels.json")
        
        print(f"✅ 标签映射加载成功")
        print(f"  - 实体标签数量: {len(entity_map)}")
        print(f"  - 关系标签数量: {len(relation_map)}")
        
        # 检查标签值范围
        entity_values = list(entity_map.values())
        relation_values = list(relation_map.values())
        
        print(f"  - 实体标签值范围: [{min(entity_values)}, {max(entity_values)}]")
        print(f"  - 关系标签值范围: [{min(relation_values)}, {max(relation_values)}]")
        
        # 验证是否是连续的0-based索引
        expected_entity_values = list(range(len(entity_map)))
        expected_relation_values = list(range(len(relation_map)))
        
        if sorted(entity_values) == expected_entity_values:
            print(f"✅ 实体标签索引一致性检查通过 (0-based连续)")
        else:
            print(f"❌ 实体标签索引不一致: 期望{expected_entity_values}, 实际{sorted(entity_values)}")
            
        if sorted(relation_values) == expected_relation_values:
            print(f"✅ 关系标签索引一致性检查通过 (0-based连续)")
        else:
            print(f"❌ 关系标签索引不一致: 期望{expected_relation_values}, 实际{sorted(relation_values)}")
            
        return True
        
    except Exception as e:
        print(f"❌ 标签一致性测试失败: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("🚀 开始验证修复后的评估指标计算...")
    
    # 测试1: 标签索引一致性
    test1_passed = test_label_consistency()
    
    # 测试2: compute_metrics函数
    test2_passed = test_compute_metrics()
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"  - 标签索引一致性: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  - compute_metrics函数: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 所有测试通过! 修复成功!")
        print(f"\n💡 建议:")
        print(f"  1. 现在可以重新训练模型，观察span_f1和relation_f1是否有显著提升")
        print(f"  2. 在训练过程中关注验证指标的变化趋势")
        print(f"  3. 如果指标仍然较低，可能需要检查数据质量或模型架构")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修复代码")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    main()
