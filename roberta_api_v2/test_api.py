#!/usr/bin/env python3
"""
RoBERTa API V2 测试脚本
"""

import requests
import json
import time
import argparse
from typing import List, Dict, Any

class APITester:
    def __init__(self, base_url: str = "http://************:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def test_health(self) -> bool:
        """测试健康检查端点"""
        print("🔍 测试健康检查...")
        try:
            response = self.session.get(f"{self.base_url}/api/v2/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过")
                print(f"   状态: {data.get('status')}")
                print(f"   模型版本: {data.get('model_version')}")
                print(f"   API版本: {data.get('api_version')}")
                print(f"   GPU可用: {data.get('gpu_available')}")
                print(f"   模型加载状态: {data.get('models_loaded')}")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                print(f"   响应: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_predict(self, text: str) -> Dict[str, Any]:
        """测试预测端点"""
        print(f"🧪 测试预测: '{text}'")
        try:
            payload = {"text": text}
            start_time = time.time()
            
            response = self.session.post(
                f"{self.base_url}/api/v2/predict",
                json=payload,
                timeout=30
            )
            
            request_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 预测成功 (请求时间: {request_time:.1f}ms)")
                
                if data.get('success'):
                    result_data = data.get('data', {})
                    print(f"   📝 输入文本: {result_data.get('text')}")
                    print(f"   🎯 语法分类: {result_data.get('grammar', {}).get('predicted', [])}")
                    print(f"   🏷️  子域分类: {result_data.get('subdomain', {}).get('predicted', 'unknown')}")
                    
                    entities = result_data.get('entities', [])
                    print(f"   🔍 实体识别: {len(entities)} 个")
                    for i, entity in enumerate(entities, 1):
                        print(f"      {i}. {entity.get('type')}: '{entity.get('text')}' (位置: {entity.get('start')}-{entity.get('end')})")
                    
                    relations = result_data.get('relations', [])
                    print(f"   🔗 关系抽取: {len(relations)} 个")
                    for i, relation in enumerate(relations, 1):
                        entity1 = relation.get('entity1', {})
                        entity2 = relation.get('entity2', {})
                        print(f"      {i}. {entity1.get('text')} --[{relation.get('relation')}]--> {entity2.get('text')}")
                    
                    print(f"   ⚙️  处理时间: {data.get('processing_time_ms', 0):.1f}ms")
                    print(f"   📊 模型版本: {data.get('model_version')}")
                
                return data
            else:
                print(f"❌ 预测失败: HTTP {response.status_code}")
                print(f"   响应: {response.text}")
                return {"error": f"HTTP {response.status_code}", "response": response.text}
                
        except Exception as e:
            print(f"❌ 预测异常: {e}")
            return {"error": str(e)}
    
    def test_batch_predict(self, texts: List[str]) -> List[Dict[str, Any]]:
        """批量测试预测"""
        print(f"📦 批量测试 {len(texts)} 个句子...")
        results = []
        
        for i, text in enumerate(texts, 1):
            print(f"\n--- 测试 {i}/{len(texts)} ---")
            result = self.test_predict(text)
            results.append(result)
            time.sleep(0.5)  # 避免请求过快
        
        return results
    
    def test_error_cases(self):
        """测试错误情况"""
        print("\n🚨 测试错误情况...")
        
        # 测试空文本
        print("\n1. 测试空文本:")
        self.test_predict("")
        
        # 测试过长文本
        print("\n2. 测试过长文本:")
        long_text = "测试" * 300  # 超过512字符限制
        self.test_predict(long_text)
        
        # 测试特殊字符
        print("\n3. 测试特殊字符:")
        self.test_predict("!@#$%^&*()_+{}|:<>?[]\\;'\",./<>?")

def main():
    parser = argparse.ArgumentParser(description="RoBERTa API V2 测试工具")
    parser.add_argument("--url", default="http://************:8000", help="API基础URL")
    parser.add_argument("--text", help="测试单个文本")
    parser.add_argument("--health", action="store_true", help="只进行健康检查")
    parser.add_argument("--errors", action="store_true", help="测试错误情况")
    
    args = parser.parse_args()
    
    tester = APITester(args.url)
    
    print("🧪 RoBERTa API V2 测试工具")
    print("=" * 50)
    print(f"🌐 API地址: {args.url}")
    
    # 健康检查
    if not tester.test_health():
        print("❌ 健康检查失败，退出测试")
        return
    
    if args.health:
        print("✅ 健康检查完成")
        return
    
    # 单个文本测试
    if args.text:
        print(f"\n📝 单个文本测试:")
        tester.test_predict(args.text)
        return
    
    # 错误情况测试
    if args.errors:
        tester.test_error_cases()
        return
    
    # 默认测试用例
    test_sentences = [
        "合同H-6632的最近一批的物料货期要多久？",
        "项目P-19298374的服务器在哪个柜子上",
        "华为项目的设备是否都已经发货了？",
        "录波仪在哪个箱号？",
        "什么是机器学习的定义？",
        "为什么系统响应时间这么慢？"
    ]
    
    print(f"\n📝 批量测试:")
    results = tester.test_batch_predict(test_sentences)
    
    # 统计结果
    successful_tests = sum(1 for r in results if r.get('success', False))
    print(f"\n📊 测试统计:")
    print(f"   总测试数: {len(results)}")
    print(f"   成功数: {successful_tests}")
    print(f"   失败数: {len(results) - successful_tests}")
    print(f"   成功率: {successful_tests/len(results)*100:.1f}%")
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    main()
