#!/usr/bin/env python3
"""
统一推理API - 提供简单的函数接口用于程序化调用

该模块提供了简化的API接口，可以轻松集成到其他Python程序中。
"""

import torch
import os
import sys
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from transformers import RobertaTokenizer, BertTokenizer, AutoTokenizer
    from my_roberta_grammar_subdomainv1 import RoBERTaForConversationClassification as GrammarSubdomainModel
    from my_roberta_entity_relationV2 import RoBERTaForConversationClassification as EntityRelationModel
    MODELS_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入模型类，将使用模拟模式: {e}")
    MODELS_AVAILABLE = False

class UnifiedInferenceAPI:
    """
    统一推理API类 - 提供简化的推理接口
    """
    
    def __init__(self, grammar_model_path=None, entity_model_path=None):
        """
        初始化API

        Args:
            grammar_model_path: 语法/子域模型路径
            entity_model_path: 实体/关系模型路径
        """
        if not MODELS_AVAILABLE:
            raise ImportError("模型类导入失败，无法初始化推理API")

        self.grammar_model = None
        self.entity_model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        self.grammar_model_path = grammar_model_path
        self.entity_model_path = entity_model_path
        self._load_models()
    
    def _load_label_mappings(self):
        """加载标签映射文件并设置全局变量"""
        # 标签文件路径 (从roberta_api_v2目录向上找datasets目录)
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))  # roberta_api_v2目录
        project_root = os.path.dirname(current_dir)  # 项目根目录
        datasets_dir = os.path.join(project_root, "datasets")

        label_files = {
            "grammar": os.path.join(datasets_dir, "my_roberta_grammar_labels.json"),
            "subdomain": os.path.join(datasets_dir, "my_roberta_subdomain_labels.json"),
            "entity": os.path.join(datasets_dir, "my_roberta_entity_labels.json"),
            "relation": os.path.join(datasets_dir, "my_roberta_relation_labels.json")
        }

        # 导入模型模块以设置全局变量
        import my_roberta_grammar_subdomainv1 as grammar_module
        import my_roberta_entity_relationV2 as entity_module

        try:
            # 使用模型类的load_labels_fromfile方法加载标签
            from my_roberta_grammar_subdomainv1 import RoBERTaForConversationClassification

            # 加载语法标签
            if os.path.exists(label_files["grammar"]):
                grammar_map, grammar_array = RoBERTaForConversationClassification.load_labels_fromfile(label_files["grammar"])
                grammar_module.CONVERSATION_GRAMMAR_ID_MAP = grammar_map
                grammar_module.CONVERSATION_ID_GRAMMAR_ARY = grammar_array

            # 加载子域标签
            if os.path.exists(label_files["subdomain"]):
                subdomain_map, subdomain_array = RoBERTaForConversationClassification.load_labels_fromfile(label_files["subdomain"])
                grammar_module.CONVERSATION_SUBDOMAIN_ID_MAP = subdomain_map
                grammar_module.CONVERSATION_ID_SUBDOMAIN_ARY = subdomain_array

            # 加载实体标签
            if os.path.exists(label_files["entity"]):
                entity_map, entity_array = RoBERTaForConversationClassification.load_labels_fromfile(label_files["entity"])
                entity_module.CONVERSATION_ENTITY_ID_MAP = entity_map
                entity_module.CONVERSATION_ID_ENTITY_ARY = entity_array

            # 加载关系标签
            if os.path.exists(label_files["relation"]):
                relation_map, relation_array = RoBERTaForConversationClassification.load_labels_fromfile(label_files["relation"])
                entity_module.CONVERSATION_RELATION_ID_MAP = relation_map
                entity_module.CONVERSATION_ID_RELATION_ARY = relation_array

        except Exception as e:
            print(f"⚠️  标签映射加载失败: {e}")

    def _find_local_tokenizer(self):
        """查找本地分词器文件"""
        tokenizer_candidates = []

        # 添加模型目录
        if self.grammar_model_path:
            tokenizer_candidates.append(self.grammar_model_path)
        if self.entity_model_path:
            tokenizer_candidates.append(self.entity_model_path)

        # 检查每个候选路径
        for path in tokenizer_candidates:
            if os.path.exists(path):
                # 检查是否包含分词器文件
                required_files = ["tokenizer_config.json"]
                if all(os.path.exists(os.path.join(path, f)) for f in required_files):
                    return path

        return None

    def _load_models(self):
        """内部方法：加载模型（完全离线模式）"""
        try:
            # 首先加载标签映射
            self._load_label_mappings()

            # 查找并加载本地分词器
            tokenizer_path = self._find_local_tokenizer()
            if tokenizer_path:
                # 尝试自动检测分词器类型
                try:
                    self.tokenizer = AutoTokenizer.from_pretrained(
                        tokenizer_path,
                        local_files_only=True
                    )
                except Exception:
                    try:
                        self.tokenizer = BertTokenizer.from_pretrained(
                            tokenizer_path,
                            local_files_only=True
                        )
                    except Exception:
                        self.tokenizer = RobertaTokenizer.from_pretrained(
                            tokenizer_path,
                            local_files_only=True
                        )

            # 加载模型
            if self.grammar_model_path and os.path.exists(self.grammar_model_path):
                self.grammar_model = GrammarSubdomainModel.from_pretrained(
                    self.grammar_model_path,
                    local_files_only=True
                )
                self.grammar_model.to(self.device)
                self.grammar_model.eval()

            if self.entity_model_path and os.path.exists(self.entity_model_path):
                self.entity_model = EntityRelationModel.from_pretrained(
                    self.entity_model_path,
                    local_files_only=True
                )
                self.entity_model.to(self.device)
                self.entity_model.eval()

        except Exception as e:
            raise RuntimeError(f"模型加载失败: {e}")
    

    

    
    def inference(self, sentence, **kwargs):
        """
        统一推理接口

        Args:
            sentence: 输入句子
            **kwargs: 其他参数（阈值等）

        Returns:
            dict: 统一的推理结果
        """
        # 使用实际模型推理
        grammar_results = {"grammar": [], "subdomain": "未知"}
        entity_results = {"entities": [], "relations": []}

        if self.grammar_model and self.tokenizer:
            try:
                results = self.grammar_model.inference(
                    sentences=[sentence],
                    tokenizer=self.tokenizer,
                    grammar_threshold=kwargs.get('grammar_threshold', 0.5)
                )
                grammar_results = {
                    "grammar": results[0]["grammar"],
                    "subdomain": results[0]["subdomain"]
                }
            except Exception as e:
                grammar_results["error"] = f"语法/子域推理失败: {e}"

        if self.entity_model and self.tokenizer:
            try:
                results = self.entity_model.inference(
                    sentences=[sentence],
                    tokenizer=self.tokenizer,
                    start_threshold=kwargs.get('start_threshold', 0.5),
                    end_threshold=kwargs.get('end_threshold', 0.5)
                )
                entity_results = {
                    "entities": results[0]["entities"],
                    "relations": results[0]["relations"]
                }
            except Exception as e:
                entity_results["error"] = f"实体/关系推理失败: {e}"

        # 合并结果
        result = {
            "sentence": sentence,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "grammar": grammar_results.get("grammar", []),
            "subdomain": grammar_results.get("subdomain", "未知"),
            "entities": entity_results.get("entities", []),
            "relations": entity_results.get("relations", []),
            "device": str(self.device)
        }
        
        # 添加错误信息
        errors = []
        if "error" in grammar_results:
            errors.append(grammar_results["error"])
        if "error" in entity_results:
            errors.append(entity_results["error"])
        if errors:
            result["errors"] = errors
            
        return result

# 全局API实例
_api_instance = None

def get_api_instance(grammar_model_path=None, entity_model_path=None):
    """
    获取API实例（单例模式）

    Args:
        grammar_model_path: 语法/子域模型路径
        entity_model_path: 实体/关系模型路径

    Returns:
        UnifiedInferenceAPI: API实例
    """
    global _api_instance

    if _api_instance is None:
        # 自动查找模型路径
        if grammar_model_path is None:
            grammar_candidates = [
                "./my_models/my_roberta_grammar_subdomain_v1_20250729_021156",
            ]
            for path in grammar_candidates:
                if os.path.exists(path):
                    grammar_model_path = path
                    break

        if entity_model_path is None:
            entity_candidates = [
                "./my_models/my_roberta_entity_relation_v1_20250728_071806",
            ]
            for path in entity_candidates:
                if os.path.exists(path):
                    entity_model_path = path
                    break

        # 检查模型文件是否存在
        if not grammar_model_path or not os.path.exists(grammar_model_path):
            raise FileNotFoundError(f"语法/子域模型文件不存在: {grammar_model_path}")
        if not entity_model_path or not os.path.exists(entity_model_path):
            raise FileNotFoundError(f"实体/关系模型文件不存在: {entity_model_path}")

        _api_instance = UnifiedInferenceAPI(
            grammar_model_path=grammar_model_path,
            entity_model_path=entity_model_path
        )

    return _api_instance

def unified_inference(sentence, **kwargs):
    """
    便捷的统一推理函数
    
    Args:
        sentence: 输入句子
        **kwargs: 其他参数
        
    Returns:
        dict: 推理结果
    """
    api = get_api_instance()
    return api.inference(sentence, **kwargs)

def batch_inference(sentences, **kwargs):
    """
    批量推理函数
    
    Args:
        sentences: 句子列表
        **kwargs: 其他参数
        
    Returns:
        list: 推理结果列表
    """
    api = get_api_instance()
    return [api.inference(sentence, **kwargs) for sentence in sentences]


