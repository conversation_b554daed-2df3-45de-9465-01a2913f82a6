# RoBERTa API V2 部署指南

## 🎯 部署概述

本文档提供RoBERTa API V2的完整部署指南，包括环境配置、服务启动和测试验证。

## 📋 部署前检查

### 1. 环境要求
- Python 3.7+
- PyTorch 1.9+ (支持CUDA)
- 至少8GB GPU内存
- 网络端口8000可用

### 2. 文件依赖检查
```bash
# 检查模型文件
ls -la ../my_models/my_roberta_grammar_subdomain_v1_20250729_021156/
ls -la ../my_models/my_roberta_entity_relation_v1_20250728_071806/

# 检查标签文件
ls -la ../datasets/my_roberta_*_labels.json

# 检查统一推理API
ls -la ../unified_inference_api.py
```

### 3. 依赖安装
```bash
cd /root/TrainQuestionIntentModel/roberta_api_v2
pip install -r requirements.txt
```

## 🚀 启动服务

### 方法1: 使用启动脚本
```bash
cd /root/TrainQuestionIntentModel/roberta_api_v2
./start.sh [HOST] [PORT] [WORKERS]

# 示例
./start.sh 0.0.0.0 8000 1
```

### 方法2: 直接启动
```bash
cd /root/TrainQuestionIntentModel/roberta_api_v2
/opt/conda/envs/wpk_modeltrain/bin/python main.py --host 0.0.0.0 --port 8000
```

### 方法3: 后台运行
```bash
cd /root/TrainQuestionIntentModel/roberta_api_v2
nohup /opt/conda/envs/wpk_modeltrain/bin/python main.py --host 0.0.0.0 --port 8000 > api_v2.log 2>&1 &
```

## ✅ 启动成功标志

看到以下输出表示启动成功：
```
🚀 启动RoBERTa API V2服务
📍 地址: http://0.0.0.0:8000
📖 文档: http://0.0.0.0:8000/docs
正在初始化统一推理服务...
统一推理服务初始化成功
  - 语法/子域模型: 已加载
  - 实体/关系模型: 已加载
  - 分词器: 已加载
  - 设备: cuda
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000
```

## 🔧 网络配置

### IP地址配置

根据您的网络环境，API可能在以下地址可用：

1. **本地访问**: `http://127.0.0.1:8000`
2. **容器内访问**: `http://0.0.0.0:8000`
3. **网络访问**: `http://[YOUR_SERVER_IP]:8000`

### 查找正确的IP地址
```bash
# 查看网络接口
ip addr show

# 查看容器IP (如果在Docker中)
hostname -I

# 查看外部IP
curl ifconfig.me
```

### 防火墙配置
确保端口8000已开放：
```bash
# Ubuntu/Debian
sudo ufw allow 8000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

## 🧪 验证部署

### 1. 健康检查
```bash
# 本地测试
curl http://127.0.0.1:8000/api/v2/health

# 网络测试 (替换为实际IP)
curl http://YOUR_SERVER_IP:8000/api/v2/health
```

**期望响应**:
```json
{
  "status": "healthy",
  "model_version": "dual_task_models_v2",
  "api_version": "v2",
  "gpu_available": true,
  "models_loaded": {
    "grammar_subdomain": true,
    "entity_relation": true,
    "tokenizer": true
  }
}
```

### 2. 预测测试
```bash
# 本地测试
curl -X POST "http://127.0.0.1:8000/api/v2/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-94871234的项目经理是谁？"}'

# 网络测试 (替换为实际IP)
curl -X POST "http://YOUR_SERVER_IP:8000/api/v2/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "项目P-94871234的项目经理是谁？"}'
```

### 3. 使用测试脚本
```bash
cd /root/TrainQuestionIntentModel/roberta_api_v2

# 简单测试
python simple_test.py

# 完整测试
python test_api.py --url http://YOUR_SERVER_IP:8000
```

## 🐛 故障排除

### 常见问题

1. **模型加载失败**
   ```
   错误: 统一推理服务初始化失败
   解决: 检查模型文件路径和权限
   ```

2. **端口占用**
   ```bash
   # 查看端口占用
   netstat -tlnp | grep 8000
   
   # 杀死占用进程
   sudo kill -9 PID
   ```

3. **GPU内存不足**
   ```bash
   # 检查GPU使用情况
   nvidia-smi
   
   # 清理GPU内存
   sudo fuser -v /dev/nvidia*
   ```

4. **网络连接问题**
   ```bash
   # 测试端口连通性
   telnet YOUR_SERVER_IP 8000
   
   # 检查防火墙状态
   sudo ufw status
   ```

### 日志查看

```bash
# 实时查看日志
tail -f api_v2.log

# 查看错误日志
grep -i error api_v2.log

# 查看启动日志
head -50 api_v2.log
```

## 📊 性能监控

### 系统资源监控
```bash
# CPU和内存使用
htop

# GPU使用情况
watch -n 1 nvidia-smi

# 磁盘使用
df -h
```

### API性能测试
```bash
# 并发测试
python test_api.py

# 压力测试 (需要安装ab)
ab -n 100 -c 10 -T 'application/json' \
   -p test_data.json \
   http://YOUR_SERVER_IP:8000/api/v2/predict
```

## 🔄 服务管理

### 停止服务
```bash
# 如果是前台运行，按 Ctrl+C

# 如果是后台运行
ps aux | grep main.py
kill PID

# 或者
pkill -f "main.py"
```

### 重启服务
```bash
# 停止服务
pkill -f "main.py"

# 等待几秒
sleep 3

# 重新启动
cd /root/TrainQuestionIntentModel/roberta_api_v2
nohup /opt/conda/envs/wpk_modeltrain/bin/python main.py --host 0.0.0.0 --port 8000 > api_v2.log 2>&1 &
```

### 服务状态检查
```bash
# 检查进程
ps aux | grep main.py

# 检查端口
netstat -tlnp | grep 8000

# 检查服务响应
curl -s http://127.0.0.1:8000/api/v2/health | jq .status
```

## 📞 技术支持

如遇到部署问题，请提供以下信息：
1. 错误日志 (`api_v2.log`)
2. 系统环境 (`python --version`, `nvidia-smi`)
3. 网络配置 (`ip addr show`)
4. 文件权限 (`ls -la`)

---

**部署版本**: v2.0.0  
**更新时间**: 2025-07-29  
**支持平台**: Linux, Docker
