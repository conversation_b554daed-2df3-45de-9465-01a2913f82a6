#!/usr/bin/env python3
"""
RoBERTa 预测 API V2 - 基于统一推理系统
支持双任务模型的统一推理：语法/子域分类 + 实体/关系抽取
"""

from fastapi import FastAPI, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any, List
import uvicorn
import traceback
import time
import sys
import os
from contextlib import asynccontextmanager

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入统一推理API
from unified_inference_api import UnifiedInferenceAPI

# 全局推理服务实例
inference_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global inference_service
    # 启动时初始化推理服务
    try:
        print("正在初始化统一推理服务...")
        

        # 获取main.py文件所在的目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # 构建模型的绝对路径
        grammar_model_path = os.path.join(script_dir, "../my_models/my_roberta_grammar_subdomain_v1_20250729_021156")
        entity_model_path = os.path.join(script_dir, "../my_models/my_roberta_entity_relation_v1_20250728_071806")


        inference_service = UnifiedInferenceAPI(
            grammar_model_path=grammar_model_path,
            entity_model_path=entity_model_path
        )
        print("统一推理服务初始化成功")
        print(f"  - 语法/子域模型: {'已加载' if inference_service.grammar_model else '未加载'}")
        print(f"  - 实体/关系模型: {'已加载' if inference_service.entity_model else '未加载'}")
        print(f"  - 分词器: {'已加载' if inference_service.tokenizer else '未加载'}")
        print(f"  - 设备: {inference_service.device}")
    except Exception as e:
        print(f"统一推理服务初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise e

    yield

    # 关闭时清理资源
    print("正在关闭统一推理服务...")

# FastAPI应用配置
app = FastAPI(
    title="RoBERTa Prediction API V2",
    version="2.0.0",
    description="基于统一推理系统的RoBERTa模型预测API，支持语法/子域分类和实体/关系抽取",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class PredictRequest(BaseModel):
    text: str = Field(..., min_length=1, max_length=512, description="输入文本")
    
    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        if not v or not v.strip():
            raise ValueError('输入文本不能为空')
        return v.strip()

# 响应模型
class EntityResponse(BaseModel):
    text: str = Field(description="实体文本")
    type: str = Field(description="实体类型")
    start: int = Field(description="起始位置")
    end: int = Field(description="结束位置")

class RelationResponse(BaseModel):
    entity1: EntityResponse = Field(description="第一个实体")
    entity2: EntityResponse = Field(description="第二个实体")
    relation: str = Field(description="关系类型")

class PredictResponse(BaseModel):
    success: bool = Field(description="请求是否成功")
    data: Optional[Dict[str, Any]] = Field(description="预测结果数据")
    model_version: str = Field(description="模型版本")
    api_version: str = Field(description="API版本")
    processing_time_ms: float = Field(description="处理时间(毫秒)")

class ErrorResponse(BaseModel):
    success: bool = Field(default=False, description="请求是否成功")
    error_type: str = Field(description="错误类型")
    error_message: str = Field(description="错误信息")
    error_details: Optional[str] = Field(description="错误详情")
    api_version: str = Field(description="API版本")

# API端点
@app.get("/")
async def root():
    """API根端点"""
    return {
        "message": "RoBERTa Prediction API V2",
        "version": "2.0.0",
        "description": "基于统一推理系统的文本分类和实体识别API",
        "endpoints": {
            "health": "/api/v2/health",
            "predict": "/api/v2/predict",
            "docs": "/docs"
        }
    }

@app.get("/api/v2/health")
async def health_check():
    """健康检查端点"""
    try:
        if inference_service is None:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "error": "推理服务未初始化",
                    "api_version": "v2"
                }
            )
        
        return {
            "status": "healthy",
            "model_version": "dual_task_models_v2",
            "api_version": "v2",
            "gpu_available": "cuda" in str(inference_service.device),
            "models_loaded": {
                "grammar_subdomain": inference_service.grammar_model is not None,
                "entity_relation": inference_service.entity_model is not None,
                "tokenizer": inference_service.tokenizer is not None
            }
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "unhealthy",
                "error": str(e),
                "api_version": "v2"
            }
        )

@app.post("/api/v2/predict", response_model=PredictResponse)
async def predict(request: PredictRequest):
    """文本预测端点"""
    start_time = time.time()
    
    try:
        if inference_service is None:
            raise HTTPException(
                status_code=503,
                detail="推理服务不可用"
            )
        
        # 执行统一推理
        result = inference_service.inference(request.text)

        # 添加调试信息
        print(f"推理结果: {result}")
        
        # 处理时间计算
        processing_time = (time.time() - start_time) * 1000
        
        # 格式化响应数据
        response_data = {
            "text": request.text,
            "grammar": {
                "predicted": result.get("grammar", []),
                "confidence": 0.95  # 暂时使用固定置信度
            },
            "subdomain": {
                "predicted": result.get("subdomain", "unknown"),
                "confidence": 0.88
            },
            "entities": [
                {
                    "text": entity.get("value", ""),
                    "type": entity.get("type", ""),
                    "start": entity.get("start", 0),
                    "end": entity.get("end", 0),
                    "confidence": 0.92
                }
                for entity in result.get("entities", [])
            ],
            "relations": [
                {
                    "entity1": {
                        "text": relation.get("entity1", {}).get("value", ""),
                        "type": relation.get("entity1", {}).get("type", ""),
                        "start": relation.get("entity1", {}).get("start", 0),
                        "end": relation.get("entity1", {}).get("end", 0)
                    },
                    "entity2": {
                        "text": relation.get("entity2", {}).get("value", ""),
                        "type": relation.get("entity2", {}).get("type", ""),
                        "start": relation.get("entity2", {}).get("start", 0),
                        "end": relation.get("entity2", {}).get("end", 0)
                    },
                    "relation": relation.get("relation_type", ""),
                    "confidence": 0.85
                }
                for relation in result.get("relations", [])
            ]
        }
        
        return PredictResponse(
            success=True,
            data=response_data,
            model_version="dual_task_models_v2",
            api_version="v2",
            processing_time_ms=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        error_details = traceback.format_exc()
        
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error_type": "inference_error",
                "error_message": f"推理过程发生错误: {str(e)}",
                "error_details": error_details,
                "api_version": "v2",
                "processing_time_ms": processing_time
            }
        )

# 异常处理器
@app.exception_handler(422)
async def validation_exception_handler(request, exc):
    """处理验证错误"""
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "error_type": "validation_error",
            "error_message": "输入数据验证失败",
            "error_details": str(exc),
            "api_version": "v2"
        }
    )

@app.exception_handler(500)
async def internal_server_error_handler(request, exc):
    """处理内部服务器错误"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error_type": "system_error",
            "error_message": "系统内部错误",
            "error_details": str(exc),
            "api_version": "v2"
        }
    )

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="启动RoBERTa API V2服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    
    args = parser.parse_args()
    
    print(f"🚀 启动RoBERTa API V2服务")
    print(f"📍 地址: http://{args.host}:{args.port}")
    print(f"📖 文档: http://{args.host}:{args.port}/docs")
    
    uvicorn.run(
        "main:app",
        host=args.host,
        port=args.port,
        workers=args.workers,
        reload=False
    )
