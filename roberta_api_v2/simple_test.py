#!/usr/bin/env python3
import requests
import json

def test_api():
    base_url = "http://172.17.6.100:8000"
    
    # 测试健康检查
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/api/v2/health", timeout=10)
        print(f"健康检查状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"服务状态: {data.get('status')}")
            print(f"模型加载: {data.get('models_loaded')}")
        else:
            print(f"健康检查失败: {response.text}")
            return
    except Exception as e:
        print(f"健康检查异常: {e}")
        return
    
    # 测试预测
    print("\n🧪 测试预测...")
    test_text = "项目P-94871234的项目经理是谁？"
    try:
        response = requests.post(
            f"{base_url}/api/v2/predict",
            json={"text": test_text},
            timeout=30
        )
        print(f"预测状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"预测成功: {data.get('success')}")
            if data.get('success'):
                result_data = data.get('data', {})
                print(f"输入文本: {result_data.get('text')}")
                print(f"语法分类: {result_data.get('grammar', {}).get('predicted', [])}")
                print(f"子域分类: {result_data.get('subdomain', {}).get('predicted', 'unknown')}")
                print(f"实体数量: {len(result_data.get('entities', []))}")
                print(f"关系数量: {len(result_data.get('relations', []))}")
                print(f"处理时间: {data.get('processing_time_ms', 0):.1f}ms")
            else:
                print(f"预测失败: {data}")
        else:
            print(f"预测请求失败: {response.text}")
    except Exception as e:
        print(f"预测异常: {e}")

if __name__ == "__main__":
    test_api()
