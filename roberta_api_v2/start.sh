#!/bin/bash

# RoBERTa API V2 启动脚本

# 设置默认参数
HOST=${1:-"0.0.0.0"}
PORT=${2:-8000}
WORKERS=${3:-1}

# 检查Python环境
PYTHON_CMD="/opt/conda/envs/wpk_modeltrain/bin/python"
if [ ! -f "$PYTHON_CMD" ]; then
    echo "⚠️  指定的Python环境不存在，使用系统默认Python"
    PYTHON_CMD="python3"
fi

# 检查当前目录
if [ ! -f "main.py" ]; then
    echo "❌ 错误: 请在roberta_api_v2目录下运行此脚本"
    exit 1
fi

# 检查依赖文件
if [ ! -f "../unified_inference_api.py" ]; then
    echo "❌ 错误: 找不到统一推理API文件"
    exit 1
fi

echo "🚀 启动RoBERTa API V2服务"
echo "📍 主机: $HOST"
echo "📍 端口: $PORT"
echo "👥 工作进程: $WORKERS"
echo "🐍 Python: $PYTHON_CMD"
echo "📁 工作目录: $(pwd)"
echo "----------------------------------------"

# 启动服务
exec $PYTHON_CMD main.py --host $HOST --port $PORT --workers $WORKERS
