# RoBERTa 预测 API V2 - 统一推理版本

基于统一推理系统的RoBERTa模型文本分类和实体识别API服务，整合了双任务模型的完整功能。

## 🎯 功能特性

- **语法分类**: query(查询)、reason(原因分析)等多种语法类型
- **子域分类**: delivery(发货)、baseinfo(基本信息)、contract(合同)等业务子域
- **实体识别**: 识别78种实体类型（项目、人员、角色、系统、合同等）
- **关系抽取**: 识别17种实体间关系
- **统一推理**: 基于双任务模型的统一推理引擎
- **版本化API**: 支持 `/api/v2/predict` 端点
- **完全离线**: 无需网络连接，使用本地模型文件

## 📁 文件结构

```
roberta_api_v2/
├── main.py              # FastAPI应用主文件
├── requirements.txt     # Python依赖
├── start.sh            # 启动脚本
├── test_api.py         # 测试脚本
└── README.md           # 本文档
```

## 🚀 快速开始

### 1. 环境准备

确保已安装依赖：
```bash
pip install -r requirements.txt
```

### 2. 启动API服务

```bash
# 进入API V2目录
cd /root/TrainQuestionIntentModel/roberta_api_v2

# 使用指定的Python环境启动服务
/opt/conda/envs/wpk_modeltrain/bin/python main.py

# 或者使用启动脚本
./start.sh

# 指定端口启动
./start.sh 0.0.0.0 8000
```

**启动成功标志**：
```
🚀 启动RoBERTa API V2服务
📍 地址: http://0.0.0.0:8000
📖 文档: http://0.0.0.0:8000/docs
正在初始化统一推理服务...
📋 正在加载标签映射文件...
  ✅ 语法标签加载成功: 3 个标签
  ✅ 子域标签加载成功: 4 个标签
  ✅ 实体标签加载成功: 78 个标签
  ✅ 关系标签加载成功: 17 个标签
统一推理服务初始化成功
INFO:     Uvicorn running on http://0.0.0.0:8000
```

### 3. 验证服务状态

```bash
# 健康检查
curl http://************:8000/api/v2/health

# 预期响应
{
  "status": "healthy",
  "model_version": "dual_task_models_v2",
  "api_version": "v2",
  "gpu_available": true,
  "models_loaded": {
    "grammar_subdomain": true,
    "entity_relation": true,
    "tokenizer": true
  }
}
```

## 📖 API文档

### 端点列表

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | API信息和文档链接 |
| `/api/v2/health` | GET | 健康检查 |
| `/api/v2/predict` | POST | 文本预测 |
| `/docs` | GET | Swagger UI 文档 |
| `/redoc` | GET | ReDoc 文档 |

### 预测请求格式

```json
{
  "text": "项目P-94871234的项目经理是谁？"
}
```

### 预测请求示例

```bash
# Linux/Mac
curl -X POST "http://************:9010/api/v2/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "合同H-6632的最近一批的物料货期要多久？"}' | jq

# Windows CMD
curl.exe -X POST "http://************:9010/api/v2/predict" -H "Content-Type: application/json" -d "{\"text\": \"项目P-94871234的项目经理是谁？\"}" | jq
```

**字段说明:**
- `text`: 输入文本，长度1-512字符，不能为空

### 预测响应格式

```json
{
  "success": true,
  "data": {
    "text": "合同H-6632的最近一批的物料货期要多久？",
    "grammar": {
      "predicted": ["query"],
      "confidence": 0.95
    },
    "subdomain": {
      "predicted": "delivery",
      "confidence": 0.88
    },
    "entities": [
      {
        "text": "h-6632",
        "type": "Contract-Id",
        "start": 3,
        "end": 6,
        "confidence": 0.92
      },
      {
        "text": "物料",
        "type": "Prj-Material-Name",
        "start": 13,
        "end": 14,
        "confidence": 0.92
      },
      {
        "text": "货期",
        "type": "Prj-Delivery-PrepareTime",
        "start": 15,
        "end": 16,
        "confidence": 0.92
      }
    ],
    "relations": [
      {
        "entity1": {
          "text": "h-6632",
          "type": "Contract-Id",
          "start": 3,
          "end": 6
        },
        "entity2": {
          "text": "物料",
          "type": "Prj-Material-Name",
          "start": 13,
          "end": 14
        },
        "relation": "prj:has_item",
        "confidence": 0.85
      }
    ]
  },
  "model_version": "dual_task_models_v2",
  "api_version": "v2",
  "processing_time_ms": 45.2
}
```

### 错误响应格式

```json
{
  "success": false,
  "error_type": "validation_error",
  "error_message": "输入文本不能为空",
  "error_details": "输入数据验证失败",
  "api_version": "v2"
}
```

**错误类型:**
- `validation_error` (422): 输入数据验证失败
- `inference_error` (500): 模型推理过程错误
- `system_error` (500): 系统内部错误

## 🧪 测试

### 运行综合测试

```bash
# 运行所有测试
python test_api.py

# 只进行健康检查
python test_api.py --health

# 测试单个文本
python test_api.py --text "你的测试文本"

# 测试错误情况
python test_api.py --errors

# 指定API地址
python test_api.py --url http://localhost:8000
```

### 测试用例

测试脚本包含以下测试用例：
- 合同查询："合同H-6632的最近一批的物料货期要多久？"
- 项目查询："项目P-19298374的服务器在哪个柜子上"
- 设备查询："华为项目的设备是否都已经发货了？"
- 物料查询："录波仪在哪个箱号？"
- 定义查询："什么是机器学习的定义？"
- 原因分析："为什么系统响应时间这么慢？"

## 🔧 配置说明

### 环境要求

- Python 3.7+
- PyTorch 1.9+
- Transformers 4.20+
- FastAPI 0.104+

### 模型文件

API服务依赖以下文件：
- 语法/子域模型: `../my_models/my_roberta_grammar_subdomain_v1_20250729_021156/`
- 实体/关系模型: `../my_models/my_roberta_entity_relation_v1_20250728_071806/`
- 标签文件: `../datasets/my_roberta_*_labels.json`
- 统一推理API: `../unified_inference_api.py`

### 端口配置

默认端口: 8000
可通过启动脚本参数修改: `./start.sh 0.0.0.0 8080`

## 🆚 与V1版本的区别

| 特性 | V1版本 | V2版本 |
|------|--------|--------|
| 模型架构 | 单一四任务模型 | 双任务模型组合 |
| 推理引擎 | 直接模型调用 | 统一推理系统 |
| 网络依赖 | 需要下载分词器 | 完全离线运行 |
| API端点 | `/api/v1/predict` | `/api/v2/predict` |
| 模型版本 | optimized_model_random | dual_task_models_v2 |
| 标签映射 | 内置在模型中 | 外部文件加载 |

## 🐛 故障排除

### 常见问题

1. **统一推理服务初始化失败**
   - 检查模型文件是否存在
   - 确认标签文件完整
   - 验证unified_inference_api.py路径

2. **模型加载失败**
   - 检查GPU内存是否足够
   - 确认PyTorch版本兼容性
   - 可尝试CPU模式运行

3. **导入错误**
   - 确认在正确目录启动
   - 检查PYTHONPATH设置
   - 验证依赖包安装

### 调试模式

启动时添加详细日志：
```bash
python main.py --host 0.0.0.0 --port 8000
```

## 📞 支持

如遇问题，请检查：
1. 控制台错误日志
2. 模型和数据文件完整性
3. Python环境和依赖包
4. 网络和端口占用情况

---

**版本**: v2.0.0  
**模型**: dual_task_models_v2  
**更新时间**: 2025-07-29
