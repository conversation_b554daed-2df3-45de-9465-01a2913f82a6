#!/usr/bin/env python3
"""
RoBERTa API V2 最终验证测试
验证API V2的完整功能和用户指定的curl命令
"""

import requests
import json
import time
import subprocess
import sys

def test_curl_command():
    """测试用户指定的curl命令"""
    print("🎯 测试用户指定的curl命令...")
    
    # 测试本地IP
    local_urls = [
        "http://127.0.0.1:8000",
        "http://0.0.0.0:8000",
        "http://localhost:8000"
    ]
    
    for url in local_urls:
        print(f"\n📍 测试URL: {url}")
        try:
            # 健康检查
            response = requests.get(f"{url}/api/v2/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ {url} 可访问")
                
                # 测试预测
                test_data = {"text": "项目P-94871234的项目经理是谁？"}
                pred_response = requests.post(
                    f"{url}/api/v2/predict",
                    json=test_data,
                    timeout=10
                )
                
                if pred_response.status_code == 200:
                    result = pred_response.json()
                    print(f"✅ 预测成功")
                    print(f"   响应格式: {'正确' if result.get('success') else '错误'}")
                    print(f"   API版本: {result.get('api_version', 'unknown')}")
                    print(f"   处理时间: {result.get('processing_time_ms', 0):.1f}ms")
                    
                    # 显示curl命令
                    print(f"\n📋 等效的curl命令:")
                    print(f'curl -X POST "{url}/api/v2/predict" \\')
                    print(f'     -H "Content-Type: application/json" \\')
                    print(f'     -d \'{json.dumps(test_data, ensure_ascii=False)}\'')
                    
                    return url
                else:
                    print(f"❌ 预测失败: {pred_response.status_code}")
            else:
                print(f"❌ {url} 不可访问: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} 连接失败: {e}")
    
    return None

def test_api_functionality(base_url):
    """测试API完整功能"""
    print(f"\n🧪 测试API完整功能 ({base_url})")
    
    test_cases = [
        {
            "name": "合同查询",
            "text": "合同H-6632的最近一批的物料货期要多久？",
            "expected_grammar": ["query"],
            "expected_subdomain": "delivery"
        },
        {
            "name": "项目查询", 
            "text": "项目P-94871234的项目经理是谁？",
            "expected_grammar": ["query"],
            "expected_subdomain": "baseinfo"
        },
        {
            "name": "设备查询",
            "text": "华为项目的设备是否都已经发货了？",
            "expected_grammar": ["query"],
            "expected_subdomain": "delivery"
        },
        {
            "name": "原因分析",
            "text": "为什么系统响应时间这么慢？",
            "expected_grammar": ["reason"],
            "expected_subdomain": "baseinfo"
        }
    ]
    
    results = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {case['name']} ---")
        print(f"输入: {case['text']}")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/v2/predict",
                json={"text": case['text']},
                timeout=15
            )
            request_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result_data = data.get('data', {})
                    
                    # 提取结果
                    grammar = result_data.get('grammar', {}).get('predicted', [])
                    subdomain = result_data.get('subdomain', {}).get('predicted', 'unknown')
                    entities = result_data.get('entities', [])
                    relations = result_data.get('relations', [])
                    processing_time = data.get('processing_time_ms', 0)
                    
                    print(f"✅ 预测成功 (请求时间: {request_time:.1f}ms)")
                    print(f"   语法分类: {grammar}")
                    print(f"   子域分类: {subdomain}")
                    print(f"   实体数量: {len(entities)}")
                    print(f"   关系数量: {len(relations)}")
                    print(f"   处理时间: {processing_time:.1f}ms")
                    
                    # 显示实体详情
                    if entities:
                        print(f"   实体详情:")
                        for j, entity in enumerate(entities[:3], 1):  # 只显示前3个
                            print(f"     {j}. {entity.get('type')}: '{entity.get('text')}' ({entity.get('start')}-{entity.get('end')})")
                    
                    # 显示关系详情
                    if relations:
                        print(f"   关系详情:")
                        for j, relation in enumerate(relations[:2], 1):  # 只显示前2个
                            e1 = relation.get('entity1', {})
                            e2 = relation.get('entity2', {})
                            rel_type = relation.get('relation', '')
                            print(f"     {j}. {e1.get('text')} --[{rel_type}]--> {e2.get('text')}")
                    
                    results.append({
                        'case': case['name'],
                        'success': True,
                        'grammar': grammar,
                        'subdomain': subdomain,
                        'entities': len(entities),
                        'relations': len(relations),
                        'processing_time': processing_time
                    })
                else:
                    print(f"❌ 预测失败: {data}")
                    results.append({'case': case['name'], 'success': False, 'error': 'prediction_failed'})
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                print(f"   响应: {response.text[:200]}")
                results.append({'case': case['name'], 'success': False, 'error': f'http_{response.status_code}'})
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append({'case': case['name'], 'success': False, 'error': str(e)})
    
    return results

def print_summary(results, base_url):
    """打印测试总结"""
    print(f"\n📊 测试总结")
    print("=" * 50)
    
    successful = sum(1 for r in results if r.get('success', False))
    total = len(results)
    
    print(f"API地址: {base_url}")
    print(f"总测试数: {total}")
    print(f"成功数: {successful}")
    print(f"失败数: {total - successful}")
    print(f"成功率: {successful/total*100:.1f}%")
    
    if successful > 0:
        avg_time = sum(r.get('processing_time', 0) for r in results if r.get('success')) / successful
        print(f"平均处理时间: {avg_time:.1f}ms")
    
    print(f"\n📋 用户指定的curl命令:")
    print(f"curl -X POST \"{base_url}/api/v2/predict\" \\")
    print(f"     -H \"Content-Type: application/json\" \\")
    print(f"     -d '{{\"text\": \"项目P-94871234的项目经理是谁？\"}}' | jq")

def main():
    print("🚀 RoBERTa API V2 最终验证测试")
    print("=" * 50)
    
    # 1. 测试curl命令和找到可用URL
    working_url = test_curl_command()
    
    if not working_url:
        print("\n❌ 无法找到可用的API服务")
        print("请确保API服务已启动:")
        print("cd /root/TrainQuestionIntentModel/roberta_api_v2")
        print("./start.sh")
        sys.exit(1)
    
    # 2. 测试完整功能
    results = test_api_functionality(working_url)
    
    # 3. 打印总结
    print_summary(results, working_url)
    
    # 4. 最终状态
    successful = sum(1 for r in results if r.get('success', False))
    if successful == len(results):
        print(f"\n✅ 所有测试通过！API V2部署成功！")
    elif successful > 0:
        print(f"\n⚠️  部分测试通过，API基本可用")
    else:
        print(f"\n❌ 所有测试失败，请检查API配置")

if __name__ == "__main__":
    main()
