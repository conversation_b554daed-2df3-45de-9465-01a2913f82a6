from sklearn.metrics import accuracy_score, precision_recall_fscore_support, f1_score, confusion_matrix
import torch
import torch.nn as nn
from transformers import BertModel, Bert<PERSON>onfig, BertTokenizer, BertTokenizerFast, BertPreTrainedModel, BertForQuestionAnswering
from transformers import Trainer, TrainingArguments
from transformers import default_data_collator
import json
import os, sys 
import statistics
from datetime import datetime
# from simple_term_menu import TerminalMenu  # 可选依赖

# 当前根目录
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
# 添加根目录到系统路径
sys.path.append(ROOT_DIR)
from datasets import load_dataset, Dataset, concatenate_datasets

# 支持自定义模型保存路径
def generate_model_save_path(custom_name=None, base_dir="./my_models"):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if custom_name is None:
        custom_name = "roberta_entity_relation"

    # 清理自定义名称，移除不安全的字符
    safe_name = "".join(c for c in custom_name if c.isalnum() or c in ('-', '_')).strip()
    if not safe_name:
        safe_name = "roberta_entity_relation"

    model_dir_name = f"{safe_name}_{timestamp}"
    save_path = os.path.join(base_dir, model_dir_name)
    return save_path

class RoBERTaForEntityRelation(BertPreTrainedModel):
    """
    专注于实体提取和关系提取的双任务RoBERTa模型
    移除了grammar和subdomain分类任务，专注于核心的NLP任务
    """

    MAX_ENTITY_SENTENCE = 9
    MAX_RELATION_SENTENCE = 12
    MAX_TOKENS_PER_ENTITY = 20

    def __init__(self, config, entity_label_count=None, relation_label_count=None):
        # call parent init
        super().__init__(config)
        # core backbone model
        self.config = config
        # base roberta model
        self.bert = BertModel(config) 
        # dropout layer
        classifier_dropout = (
            config.classifier_dropout if config.classifier_dropout is not None else config.hidden_dropout_prob
        )
        self.dropout = nn.Dropout(classifier_dropout)

        # 确定各类标签的数量，支持参数传入或使用全局变量
        if entity_label_count is not None:
            num_entity_labels = entity_label_count
        else:
            try:
                num_entity_labels = len(CONVERSATION_ENTITY_ID_MAP)
            except NameError:
                num_entity_labels = 78  # 默认实体标签数量
                print(f"Warning: CONVERSATION_ENTITY_ID_MAP not defined, using default: {num_entity_labels}")

        if relation_label_count is not None:
            num_relation_labels = relation_label_count
        else:
            try:
                num_relation_labels = len(CONVERSATION_RELATION_ID_MAP)
            except NameError:
                num_relation_labels = 17  # 默认关系标签数量
                print(f"Warning: CONVERSATION_RELATION_ID_MAP not defined, using default: {num_relation_labels}")

        # Entity extraction components
        # start-end pointer based entity classifier (start + end + classifier)
        self.start_classifier = nn.Linear(config.hidden_size, 1)
        self.end_classifier = nn.Linear(config.hidden_size, 1)
        self.entity_type_classifier = nn.Linear(config.hidden_size, num_entity_labels)
        
        # Relation extraction component
        # relation between tokens, only one relation between two entities , but may be two relations between three tokens
        self.relation_classifier = nn.Linear(config.hidden_size * 3, num_relation_labels)

        # ===== 优化的双任务损失权重配置 =====
        # 专注于Entity和Relation任务，重新分配权重
        self.weight_position = 2.5     # 实体边界检测权重 (提升)
        self.weight_entity_type = 3.5  # 实体类型分类权重 (提升)
        self.weight_relation = 3.0     # 关系提取权重 (提升)

        # token classification labels
        # save conversation history and extracted key entities and properties
        # window size = 5
        self.conversation_history = []
        self.conversation_history_size = 3*3
        self.project_state = {} # upate based on conversation history
        self.project_state_size = 3

        # Initialize weights and apply final processing
        self.post_init()

    def update_loss_weights(self, weight_position=None, weight_entity_type=None, weight_relation=None):
        """更新损失权重的方法"""
        if weight_position is not None:
            self.weight_position = weight_position
        if weight_entity_type is not None:
            self.weight_entity_type = weight_entity_type
        if weight_relation is not None:
            self.weight_relation = weight_relation
        
        print(f"Updated loss weights - Position: {self.weight_position}, "
              f"Entity Type: {self.weight_entity_type}, Relation: {self.weight_relation}")

    def forward(self, 
                input_ids=None, 
                attention_mask=None, 
                token_type_ids=None, 
                entity_start_labels=None, 
                entity_end_labels=None, 
                span_entity_labels=None,  # [(start, end, label)] per batch
                entity_relation_labels=None, 
                **kwargs):
        
        batch_size = input_ids.size(0) if input_ids is not None else 1

        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask, token_type_ids=token_type_ids)
        sequence_output = outputs.last_hidden_state # [batch, seq_len, hidden]
        
        # Entity boundary detection
        start_logits = self.start_classifier(sequence_output).squeeze(-1)
        end_logits = self.end_classifier(sequence_output).squeeze(-1)

        loss_dict = {}
        
        # entity start end loss, without this, the following don't know how to span entity
        if entity_start_labels is not None and entity_end_labels is not None:
            start_loss = nn.BCEWithLogitsLoss()(start_logits, entity_start_labels.float())
            end_loss = nn.BCEWithLogitsLoss()(end_logits, entity_end_labels.float())
            loss_dict["position_loss"] = start_loss*0.5 + end_loss*0.5
        
        # span entity type loss
        span_type_losses = []
        span_logits_list = []
        if span_entity_labels is not None:
            # 获取实体标签数量，优先使用模型配置
            try:
                num_entity_labels = len(CONVERSATION_ENTITY_ID_MAP)
            except NameError:
                # 如果全局变量不存在，使用模型配置中的标签数量
                num_entity_labels = self.entity_type_classifier.out_features

            zero_span_logits = torch.zeros(num_entity_labels, device=sequence_output.device)
            zero_sentence_span_logits = torch.zeros(RoBERTaForEntityRelation.MAX_ENTITY_SENTENCE, num_entity_labels, device=sequence_output.device)
            for b_idx, spans in enumerate(span_entity_labels):
                per_sentence_span_tensor = None  # Initialize variable to store span logits tensor
                per_sentence_span_logits_list = []  # Initialize list to store span logits tensors for each sentence

                for (start, end, label) in spans:
                    # skip if any of the values are -1, because they are padding
                    if (start == -1 or end == -1 or label == -1):
                        # padding span_logits_list and span_position_list
                        per_sentence_span_logits_list.append(zero_span_logits)                        
                    else:
                        # span_emb is the mean of the entity type embeddings
                        span_emb = sequence_output[b_idx, start:end+1, :].mean(dim=0)
                        span_logits = self.entity_type_classifier(self.dropout(span_emb))
                        per_sentence_span_logits_list.append(span_logits)
                        # 修复：label现在从1开始，需要转换为0-based索引用于CrossEntropyLoss
                        assert label >= 1 and label <= num_entity_labels, \
                            f"Entity label {label} out of bounds. Expected range: [1, {num_entity_labels}]"

                        # 转换为0-based索引用于损失计算
                        label_zero_based = torch.as_tensor(label - 1, device=sequence_output.device, dtype=torch.long)
                        span_loss = nn.CrossEntropyLoss()(span_logits.unsqueeze(0), label_zero_based.unsqueeze(0))
                        span_type_losses.append(span_loss)

                per_sentence_span_tensor = torch.stack(per_sentence_span_logits_list, dim=0) if per_sentence_span_logits_list else zero_sentence_span_logits
                span_logits_list.append(per_sentence_span_tensor)  # Append the tensor for this sentence
            
            # finish loop all sentence                       
            if span_type_losses:
                loss_dict["entity_type_loss"] = torch.stack(span_type_losses).mean()
            else:
                # 即使没有实体，也要添加一个零损失，以保持计算图的连接
                loss_dict["entity_type_loss"] = torch.zeros(1, device=sequence_output.device, requires_grad=True)

            # stack span logits, if empty, create an empty tensor
            span_logits_tensor = torch.stack(span_logits_list, dim=0) if span_logits_list else torch.stack([zero_sentence_span_logits] * batch_size)
            # check shape
            assert span_logits_tensor.shape[0] == batch_size, "Span logits must have the same batch size as input_ids."
            assert span_logits_tensor.shape[1] == RoBERTaForEntityRelation.MAX_ENTITY_SENTENCE, "Span logits must have the same number of entities as defined in MAX_ENTITY_SENTENCE."
            assert span_logits_tensor.shape[2] == num_entity_labels, "Span logits must have the same number of entity types as defined in entity labels."

        # relation loss
        relation_losses = []
        relation_logits_list = []
        if entity_relation_labels is not None:
            # 获取关系标签数量，优先使用模型配置
            try:
                num_relation_labels = len(CONVERSATION_RELATION_ID_MAP)
            except NameError:
                # 如果全局变量不存在，使用模型配置中的标签数量
                num_relation_labels = self.relation_classifier.out_features

            zero_relation_logits = torch.zeros(num_relation_labels, device=sequence_output.device)
            zero_sentence_relation_logits = torch.zeros(RoBERTaForEntityRelation.MAX_RELATION_SENTENCE, num_relation_labels, device=sequence_output.device)
            for b_idx, pairs in enumerate(entity_relation_labels):
                per_sentence_relation_tensor = None  # Initialize variable to store relation logits tensor
                per_sentence_relation_logits_list = []  # Initialize list to store relation logits tensors for each sentence

                for (start_i, end_i, start_j, end_j, type_id) in pairs:
                    # skip if any of the values are -1, because they are padding
                    if (start_i == -1 or end_i == -1 or start_j == -1 or end_j == -1):
                        # padding relation logits to same length as span logits
                        per_sentence_relation_logits_list.append(zero_relation_logits)
                    else:
                        # Process each relation individually (similar to span processing)
                        emb_i = sequence_output[b_idx, start_i:end_i+1, :].mean(dim=0)
                        emb_j = sequence_output[b_idx, start_j:end_j+1, :].mean(dim=0)
                        cls_emb = sequence_output[b_idx, 0, :]
                        # Combine embeddings for this pair
                        pair_emb = torch.cat([emb_i, emb_j, cls_emb], dim=-1)
                        logits = self.relation_classifier(pair_emb)
                        per_sentence_relation_logits_list.append(logits)
                        # 修复：type_id现在从1开始，需要转换为0-based索引用于CrossEntropyLoss
                        assert type_id >= 1 and type_id <= num_relation_labels, \
                            f"Relation type_id {type_id} out of bounds. Expected range: [1, {num_relation_labels}]"

                        # 转换为0-based索引用于损失计算
                        type_id_zero_based = torch.as_tensor(type_id - 1, device=sequence_output.device, dtype=torch.long)
                        relation_loss = nn.CrossEntropyLoss()(logits.unsqueeze(0), type_id_zero_based.unsqueeze(0))
                        relation_losses.append(relation_loss)

                per_sentence_relation_tensor = torch.stack(per_sentence_relation_logits_list, dim=0) if per_sentence_relation_logits_list else zero_sentence_relation_logits
                relation_logits_list.append(per_sentence_relation_tensor)  # Append the tensor for this sentence
            
            # finish loop all sentence
            if relation_losses:
                loss_dict["relation_loss"] = torch.stack(relation_losses).mean()
            else:
                # 即使没有关系，也要添加一个零损失
                loss_dict["relation_loss"] = torch.zeros(1, device=sequence_output.device, requires_grad=True)

            # stack relation logits, if empty, create an empty tensor
            relation_logits_tensor = torch.stack(relation_logits_list, dim=0) if relation_logits_list else torch.stack([zero_sentence_relation_logits] * batch_size)
            # check shape
            assert relation_logits_tensor.shape[0] == batch_size, "Relation logits and span logits must have the same batch size."
            assert relation_logits_tensor.shape[1] == RoBERTaForEntityRelation.MAX_RELATION_SENTENCE, "Relation logits must have the same number of relations as span logits."
            assert relation_logits_tensor.shape[2] == num_relation_labels, "Relation logits must have the same number of relation types as defined in relation labels."

        # 双任务损失权重计算
        total_loss = sum([
            self.weight_position * loss_dict.get('position_loss', 0.0),
            self.weight_entity_type * loss_dict.get('entity_type_loss', 0.0),
            self.weight_relation * loss_dict.get('relation_loss', 0.0),
        ])

        # Returning both predictions as a tuple
        return total_loss, {
            "start_logits": start_logits,
            "end_logits": end_logits,
            "span_logits": span_logits_tensor if span_entity_labels is not None else None,
            "relation_logits": relation_logits_tensor if entity_relation_labels is not None else None,
            "sequence_output": sequence_output
        }

def preprocess_dataset(dataset, tokenizer, max_length=256, padding="max_length",
                      truncation=True, entity_id_map=None, relation_id_map=None):
    """
    专门为Entity和Relation双任务预处理数据集
    移除了grammar和subdomain相关的处理逻辑
    """

    # 获取标签映射，支持参数传入或使用全局变量
    if entity_id_map is not None:
        ENTITY_MAP = entity_id_map
    else:
        try:
            ENTITY_MAP = CONVERSATION_ENTITY_ID_MAP
        except NameError:
            raise ValueError("Entity ID map not provided and CONVERSATION_ENTITY_ID_MAP not defined")

    if relation_id_map is not None:
        RELATION_MAP = relation_id_map
    else:
        try:
            RELATION_MAP = CONVERSATION_RELATION_ID_MAP
        except NameError:
            raise ValueError("Relation ID map not provided and CONVERSATION_RELATION_ID_MAP not defined")

    # 提取所有句子进行批量tokenization
    sentences = [item['sentence'] for item in dataset]

    # 批量tokenization
    tokenized = tokenizer(
        sentences,
        max_length=max_length,
        padding=padding,
        truncation=truncation,
        return_tensors="pt"
    )

    batch_input_ids = tokenized['input_ids']
    batch_attention_mask = tokenized['attention_mask']

    # 初始化批量标签
    batch_size = len(dataset)
    seq_len = batch_input_ids.size(1)

    # Entity相关标签
    entity_start_labels_batch = torch.zeros(batch_size, seq_len)
    entity_end_labels_batch = torch.zeros(batch_size, seq_len)
    span_entity_labels_batch = []

    # Relation相关标签
    entity_relation_labels_batch = []

    for i, item in enumerate(dataset):
        sentence = item['sentence']

        # 获取token到字符的映射
        encoding = tokenizer(sentence, max_length=max_length, padding=padding,
                           truncation=truncation, return_offsets_mapping=True)
        offset_mapping = encoding['offset_mapping']

        # Entity spans处理
        entity_start_labels = torch.zeros(seq_len)
        entity_end_labels = torch.zeros(seq_len)
        span_entity_labels = []

        for entity in item['entity_spans']:
            char_start = entity['start']
            char_end = entity['end']
            entity_type = entity['type']

            # 查找对应的token位置
            token_start = None
            token_end = None

            for token_idx, (start_char, end_char) in enumerate(offset_mapping):
                if start_char <= char_start < end_char:
                    token_start = token_idx
                if start_char < char_end <= end_char:
                    token_end = token_idx
                    break

            if token_start is not None and token_end is not None:
                # 设置边界标签
                entity_start_labels[token_start] = 1
                entity_end_labels[token_end] = 1

                # 获取实体类型ID
                entity_type_id = ENTITY_MAP.get(entity_type, None)
                if entity_type_id is not None:
                    span_entity_labels.append((token_start, token_end, entity_type_id))

        # 填充span_entity_labels到固定长度
        while len(span_entity_labels) < RoBERTaForEntityRelation.MAX_ENTITY_SENTENCE:
            span_entity_labels.append((-1, -1, -1))

        # 截断到最大长度
        span_entity_labels = span_entity_labels[:RoBERTaForEntityRelation.MAX_ENTITY_SENTENCE]

        entity_start_labels_batch[i] = entity_start_labels
        entity_end_labels_batch[i] = entity_end_labels
        span_entity_labels_batch.append(span_entity_labels)

        # Relation pairs处理
        entity_relation_labels = []

        for relation in item['relation_pairs']:
            # 处理两种可能的数据格式
            if 'entity_i' in relation and 'entity_j' in relation:
                # 格式1: {"entity_i": {...}, "entity_j": {...}, "type": "..."}
                entity_i = relation['entity_i']
                entity_j = relation['entity_j']
                char_start_i = entity_i['start']
                char_end_i = entity_i['end']
                char_start_j = entity_j['start']
                char_end_j = entity_j['end']
            else:
                # 格式2: {"start_i": 2, "end_i": 12, "start_j": 15, "end_j": 17, "type": "..."}
                char_start_i = relation['start_i']
                char_end_i = relation['end_i']
                char_start_j = relation['start_j']
                char_end_j = relation['end_j']

            relation_type = relation['type']

            # 查找实体i的token位置
            token_start_i = None
            token_end_i = None

            for token_idx, (start_char, end_char) in enumerate(offset_mapping):
                if start_char <= char_start_i < end_char:
                    token_start_i = token_idx
                if start_char < char_end_i <= end_char:
                    token_end_i = token_idx
                    break

            # 查找实体j的token位置
            token_start_j = None
            token_end_j = None

            for token_idx, (start_char, end_char) in enumerate(offset_mapping):
                if start_char <= char_start_j < end_char:
                    token_start_j = token_idx
                if start_char < char_end_j <= end_char:
                    token_end_j = token_idx
                    break

            if (token_start_i is not None and token_end_i is not None and
                token_start_j is not None and token_end_j is not None):

                # 获取关系类型ID
                relation_type_id = RELATION_MAP.get(relation_type, None)
                if relation_type_id is not None:
                    entity_relation_labels.append((token_start_i, token_end_i,
                                                 token_start_j, token_end_j,
                                                 relation_type_id))

        # 填充entity_relation_labels到固定长度
        while len(entity_relation_labels) < RoBERTaForEntityRelation.MAX_RELATION_SENTENCE:
            entity_relation_labels.append((-1, -1, -1, -1, -1))

        # 截断到最大长度
        entity_relation_labels = entity_relation_labels[:RoBERTaForEntityRelation.MAX_RELATION_SENTENCE]
        entity_relation_labels_batch.append(entity_relation_labels)

    return {
        'input_ids': batch_input_ids,
        'attention_mask': batch_attention_mask,
        'entity_start_labels': entity_start_labels_batch,
        'entity_end_labels': entity_end_labels_batch,
        'span_entity_labels': span_entity_labels_batch,
        'entity_relation_labels': entity_relation_labels_batch
    }

def compute_metrics(eval_pred, start_threshold=0.5, end_threshold=0.5):
    """
    专门为Entity和Relation双任务计算评估指标
    移除了grammar和subdomain相关的指标计算
    """
    try:
        preds, labels = eval_pred

        # 调试信息：检查输入数据结构
        print(f"🔍 Debug - preds类型: {type(preds)}, 长度: {len(preds) if hasattr(preds, '__len__') else 'N/A'}")
        print(f"🔍 Debug - labels类型: {type(labels)}, 长度: {len(labels) if hasattr(labels, '__len__') else 'N/A'}")

        # 处理preds：可能是字典或列表/元组
        if isinstance(preds, dict):
            # 如果是字典，按键名提取预测结果
            print(f"🔍 Debug - preds是字典，键: {list(preds.keys())}")
            start_logits = preds.get('start_logits', None)
            end_logits = preds.get('end_logits', None)
            span_logits = preds.get('span_logits', None)
            relation_logits = preds.get('relation_logits', None)

            # 如果字典中没有预期的键，尝试按索引访问
            if start_logits is None and len(preds) >= 2:
                keys = list(preds.keys())
                start_logits = preds[keys[0]] if len(keys) > 0 else None
                end_logits = preds[keys[1]] if len(keys) > 1 else None
                span_logits = preds[keys[2]] if len(keys) > 2 else None
                relation_logits = preds[keys[3]] if len(keys) > 3 else None

        elif isinstance(preds, (list, tuple)):
            # 如果是列表/元组，按索引提取
            if len(preds) < 2:
                print(f"❌ Error - preds格式错误: 期望长度>=2的列表/元组，实际: {type(preds)}, 长度: {len(preds)}")
                return {'start_end_f1': 0.0, 'span_f1': 0.0, 'relation_f1': 0.0}

            start_logits = preds[0]
            end_logits = preds[1]
            span_logits = preds[2] if len(preds) > 2 and preds[2] is not None else None
            relation_logits = preds[3] if len(preds) > 3 and preds[3] is not None else None
        else:
            print(f"❌ Error - preds类型不支持: {type(preds)}")
            return {'start_end_f1': 0.0, 'span_f1': 0.0, 'relation_f1': 0.0}

        # 调试信息：显示提取的预测结果
        print(f"🔍 Debug - start_logits形状: {start_logits.shape if hasattr(start_logits, 'shape') else type(start_logits)}")
        print(f"🔍 Debug - end_logits形状: {end_logits.shape if hasattr(end_logits, 'shape') else type(end_logits)}")
        print(f"🔍 Debug - span_logits: {'存在' if span_logits is not None else '不存在'}")
        print(f"🔍 Debug - relation_logits: {'存在' if relation_logits is not None else '不存在'}")

        # 安全地重新构建labels字典，只包含Entity和Relation
        if not isinstance(labels, (list, tuple)) or len(labels) < 4:
            print(f"❌ Error - labels格式错误: 期望长度>=4的列表/元组，实际: {type(labels)}, 长度: {len(labels) if hasattr(labels, '__len__') else 'N/A'}")
            return {'start_end_f1': 0.0, 'span_f1': 0.0, 'relation_f1': 0.0}

        labels = {
            'entity_start_labels': labels[0],
            'entity_end_labels': labels[1],
            'span_entity_labels': labels[2],
            'entity_relation_labels': labels[3]
        }

    except Exception as e:
        print(f"❌ Error in compute_metrics 数据解包阶段: {e}")
        import traceback
        traceback.print_exc()
        return {'start_end_f1': 0.0, 'span_f1': 0.0, 'relation_f1': 0.0}

    # Entity边界检测F1分数
    start_end_f1 = 0.0
    try:
        if start_logits is not None and end_logits is not None:
            # 转换为二进制预测
            start_preds = (torch.sigmoid(torch.as_tensor(start_logits)) > start_threshold).int()
            end_preds = (torch.sigmoid(torch.as_tensor(end_logits)) > end_threshold).int()

            # 计算start和end的F1分数
            start_labels_flat = labels['entity_start_labels'].flatten()
            end_labels_flat = labels['entity_end_labels'].flatten()
            start_preds_flat = start_preds.flatten()
            end_preds_flat = end_preds.flatten()

            print(f"🔍 Debug - start_labels形状: {start_labels_flat.shape}, 唯一值: {torch.unique(start_labels_flat)}")
            print(f"🔍 Debug - start_preds形状: {start_preds_flat.shape}, 唯一值: {torch.unique(start_preds_flat)}")

            # 计算F1分数
            start_f1 = f1_score(start_labels_flat.cpu().numpy(), start_preds_flat.cpu().numpy(), average='binary', zero_division=0)
            end_f1 = f1_score(end_labels_flat.cpu().numpy(), end_preds_flat.cpu().numpy(), average='binary', zero_division=0)
            start_end_f1 = (start_f1 + end_f1) / 2

            print(f"🔍 Debug - start_f1: {start_f1:.4f}, end_f1: {end_f1:.4f}")

    except Exception as e:
        print(f"❌ Error in compute_metrics 实体边界检测阶段: {e}")
        import traceback
        traceback.print_exc()
        start_end_f1 = 0.0

    # Entity类型分类F1分数
    span_f1 = 0.0
    try:
        if span_logits is not None:
            span_preds = torch.argmax(torch.as_tensor(span_logits), dim=-1)
            span_labels = []
            span_preds_list = []

            print(f"🔍 Debug - span_logits形状: {span_logits.shape if hasattr(span_logits, 'shape') else type(span_logits)}")
            print(f"🔍 Debug - span_preds形状: {span_preds.shape if hasattr(span_preds, 'shape') else type(span_preds)}")

            for batch_idx, batch_spans in enumerate(labels['span_entity_labels']):
                # 处理numpy数组或列表/元组
                if hasattr(batch_spans, 'tolist'):
                    # 如果是numpy数组，转换为列表
                    batch_spans = batch_spans.tolist()
                elif not isinstance(batch_spans, (list, tuple)):
                    print(f"⚠️ Warning - batch_spans[{batch_idx}]格式不支持: {type(batch_spans)}")
                    continue

                for span_idx, span_data in enumerate(batch_spans):
                    # 处理numpy数组或列表/元组
                    if hasattr(span_data, 'tolist'):
                        span_data = span_data.tolist()
                    elif not isinstance(span_data, (list, tuple)):
                        print(f"⚠️ Warning - span_data[{batch_idx}][{span_idx}]格式不支持: {type(span_data)}")
                        continue

                    if len(span_data) != 3:
                        print(f"⚠️ Warning - span_data[{batch_idx}][{span_idx}]长度错误: {len(span_data)}, 数据: {span_data}")
                        continue

                    start, end, label = span_data
                    if start != -1 and end != -1 and label != -1:
                        # 转换为0-based索引用于评估
                        if label > 0:  # 确保标签有效
                            span_labels.append(label - 1)
                            if batch_idx < span_preds.shape[0] and span_idx < span_preds.shape[1]:
                                span_preds_list.append(span_preds[batch_idx, span_idx].item())
                            else:
                                print(f"⚠️ Warning - span_preds索引超出范围: [{batch_idx}, {span_idx}]")

            print(f"🔍 Debug - 收集到的span标签数: {len(span_labels)}, 预测数: {len(span_preds_list)}")

            if span_labels and span_preds_list and len(span_labels) == len(span_preds_list):
                span_f1 = f1_score(span_labels, span_preds_list, average='weighted', zero_division=0)
                print(f"🔍 Debug - span_f1: {span_f1:.4f}")
            else:
                print(f"⚠️ Warning - span标签和预测数量不匹配或为空")

    except Exception as e:
        print(f"❌ Error in compute_metrics 实体类型分类阶段: {e}")
        import traceback
        traceback.print_exc()
        span_f1 = 0.0

    # Relation分类F1分数
    relation_f1 = 0.0
    try:
        if relation_logits is not None:
            relation_preds = torch.argmax(torch.as_tensor(relation_logits), dim=-1)
            relation_labels = []
            relation_preds_list = []

            print(f"🔍 Debug - relation_logits形状: {relation_logits.shape if hasattr(relation_logits, 'shape') else type(relation_logits)}")
            print(f"🔍 Debug - relation_preds形状: {relation_preds.shape if hasattr(relation_preds, 'shape') else type(relation_preds)}")

            for batch_idx, batch_relations in enumerate(labels['entity_relation_labels']):
                # 处理numpy数组或列表/元组
                if hasattr(batch_relations, 'tolist'):
                    # 如果是numpy数组，转换为列表
                    batch_relations = batch_relations.tolist()
                elif not isinstance(batch_relations, (list, tuple)):
                    print(f"⚠️ Warning - batch_relations[{batch_idx}]格式不支持: {type(batch_relations)}")
                    continue

                for rel_idx, relation_data in enumerate(batch_relations):
                    # 处理numpy数组或列表/元组
                    if hasattr(relation_data, 'tolist'):
                        relation_data = relation_data.tolist()
                    elif not isinstance(relation_data, (list, tuple)):
                        print(f"⚠️ Warning - relation_data[{batch_idx}][{rel_idx}]格式不支持: {type(relation_data)}")
                        continue

                    if len(relation_data) != 5:
                        print(f"⚠️ Warning - relation_data[{batch_idx}][{rel_idx}]长度错误: {len(relation_data)}, 数据: {relation_data}")
                        continue

                    start_i, end_i, start_j, end_j, type_id = relation_data
                    if start_i != -1 and end_i != -1 and start_j != -1 and end_j != -1 and type_id != -1:
                        # 转换为0-based索引用于评估
                        if type_id > 0:  # 确保标签有效
                            relation_labels.append(type_id - 1)
                            if batch_idx < relation_preds.shape[0] and rel_idx < relation_preds.shape[1]:
                                relation_preds_list.append(relation_preds[batch_idx, rel_idx].item())
                            else:
                                print(f"⚠️ Warning - relation_preds索引超出范围: [{batch_idx}, {rel_idx}]")

            print(f"🔍 Debug - 收集到的relation标签数: {len(relation_labels)}, 预测数: {len(relation_preds_list)}")

            if relation_labels and relation_preds_list and len(relation_labels) == len(relation_preds_list):
                relation_f1 = f1_score(relation_labels, relation_preds_list, average='weighted', zero_division=0)
                print(f"🔍 Debug - relation_f1: {relation_f1:.4f}")
            else:
                print(f"⚠️ Warning - relation标签和预测数量不匹配或为空")

    except Exception as e:
        print(f"❌ Error in compute_metrics 关系分类阶段: {e}")
        import traceback
        traceback.print_exc()
        relation_f1 = 0.0

    # 最终返回结果
    try:
        result = {
            'start_end_f1': float(start_end_f1),
            'span_f1': float(span_f1),
            'relation_f1': float(relation_f1),
        }
        print(f"🔍 Debug - 最终评估结果: {result}")
        return result

    except Exception as e:
        print(f"❌ Error in compute_metrics 结果返回阶段: {e}")
        import traceback
        traceback.print_exc()
        return {'start_end_f1': 0.0, 'span_f1': 0.0, 'relation_f1': 0.0}

def inference(model, sentences, tokenizer, max_length=256, padding="max_length",
              truncation=True, start_threshold=0.5, end_threshold=0.5,
              entity_id_map=None, relation_id_map=None):
    """
    专门为Entity和Relation双任务的推理接口
    移除了grammar和subdomain的预测输出
    """
    model.eval()
    results = []

    # 获取标签映射的反向映射
    if entity_id_map is not None and relation_id_map is not None:
        entity_id_to_label = {v: k for k, v in entity_id_map.items()}
        relation_id_to_label = {v: k for k, v in relation_id_map.items()}
    else:
        try:
            entity_id_to_label = {v: k for k, v in CONVERSATION_ENTITY_ID_MAP.items()}
            relation_id_to_label = {v: k for k, v in CONVERSATION_RELATION_ID_MAP.items()}
        except NameError:
            raise ValueError("Entity and relation ID maps must be provided either as parameters or global variables")

    with torch.no_grad():
        for sentence in sentences:
            # Tokenize input
            inputs = tokenizer(
                sentence,
                max_length=max_length,
                padding=padding,
                truncation=truncation,
                return_tensors="pt"
            )

            # Get model predictions
            _, outputs = model(**inputs)

            start_logits = outputs["start_logits"]
            end_logits = outputs["end_logits"]
            sequence_output = outputs["sequence_output"]

            # Convert logits to probabilities
            start_probs = torch.sigmoid(start_logits).squeeze()
            end_probs = torch.sigmoid(end_logits).squeeze()

            # Find entity spans
            start_positions = (start_probs > start_threshold).nonzero(as_tuple=True)[0]
            end_positions = (end_probs > end_threshold).nonzero(as_tuple=True)[0]

            # Match start and end positions to form spans
            spans = []
            for start_pos in start_positions:
                for end_pos in end_positions:
                    if end_pos >= start_pos:
                        spans.append({
                            "start": start_pos.item(),
                            "end": end_pos.item(),
                            "type": "UNKNOWN",  # 将在下面确定类型
                            "value": ""  # 将在下面填充
                        })
                        break  # 取第一个有效的end位置

            # 获取token到字符的映射以提取实际文本
            encoding = tokenizer(sentence, max_length=max_length, padding=padding,
                               truncation=truncation, return_offsets_mapping=True)
            offset_mapping = encoding['offset_mapping']

            # 为每个span确定实体类型和提取文本
            for i, span in enumerate(spans):
                # 提取实际文本
                start_char = offset_mapping[span["start"]][0]
                end_char = offset_mapping[span["end"]][1]
                span["value"] = sentence[start_char:end_char]

                # 获取实体类型预测 - 需要重新计算span的embedding
                if span["start"] < len(sequence_output[0]) and span["end"] < len(sequence_output[0]):
                    # 计算span的embedding（与训练时一致）
                    span_emb = sequence_output[0, span["start"]:span["end"]+1, :].mean(dim=0)

                    # 获取实体类型logits
                    with torch.no_grad():
                        span_logits = model.entity_type_classifier(span_emb)
                        type_probs = torch.softmax(span_logits, dim=-1)
                        predicted_type_id = torch.argmax(type_probs).item()

                        # 转换为1-based索引（与训练时一致）
                        predicted_type_id_1based = predicted_type_id + 1
                        predicted_type = entity_id_to_label.get(predicted_type_id_1based, "UNKNOWN")
                        span["type"] = predicted_type
                else:
                    span["type"] = "UNKNOWN"

            # 处理关系预测
            relations = []
            if len(spans) >= 2:
                # 为每对实体预测关系
                for i in range(len(spans)):
                    for j in range(i + 1, len(spans)):
                        span_i = spans[i]
                        span_j = spans[j]

                        # 确保span位置有效
                        if (span_i["start"] < len(sequence_output[0]) and span_i["end"] < len(sequence_output[0]) and
                            span_j["start"] < len(sequence_output[0]) and span_j["end"] < len(sequence_output[0])):

                            # 计算实体embeddings（与训练时一致）
                            emb_i = sequence_output[0, span_i["start"]:span_i["end"]+1, :].mean(dim=0)
                            emb_j = sequence_output[0, span_j["start"]:span_j["end"]+1, :].mean(dim=0)
                            cls_emb = sequence_output[0, 0, :]  # CLS token

                            # 拼接embeddings
                            pair_emb = torch.cat([emb_i, emb_j, cls_emb], dim=-1)

                            # 预测关系
                            with torch.no_grad():
                                relation_logits = model.relation_classifier(pair_emb)
                                relation_probs = torch.softmax(relation_logits, dim=-1)
                                predicted_relation_id = torch.argmax(relation_probs).item()

                                # 转换为1-based索引
                                predicted_relation_id_1based = predicted_relation_id + 1
                                predicted_relation = relation_id_to_label.get(predicted_relation_id_1based, "none")

                                # 只添加非"none"和"unknown"关系，且置信度较高的关系
                                confidence = float(torch.max(relation_probs))
                                if (predicted_relation not in ["none", "unknown"] and confidence > 0.3):
                                    relations.append({
                                        "entity_i": span_i,
                                        "entity_j": span_j,
                                        "type": predicted_relation,
                                        "confidence": confidence
                                    })

            results.append({
                "sentence": sentence,
                "entities": [
                    {"start": s["start"], "end": s["end"], "type": s["type"], "value": s["value"]}
                    for s in spans
                ],
                "relations": relations
            })

    return results

def load_labels_fromfile(entity_labels_file="datasets/my_roberta_entity_labels.json",
                        relation_labels_file="datasets/my_roberta_relation_labels.json"):
    """
    从文件加载Entity和Relation标签映射
    """
    # 加载实体标签
    with open(entity_labels_file, 'r', encoding='utf-8') as f:
        entity_labels_data = json.load(f)

    # 加载关系标签
    with open(relation_labels_file, 'r', encoding='utf-8') as f:
        relation_labels_data = json.load(f)

    # 提取标签名称（处理字典列表格式）
    if isinstance(entity_labels_data, list) and len(entity_labels_data) > 0:
        if isinstance(entity_labels_data[0], dict):
            # 字典列表格式：[{"label": "Person", "desc": "..."}, ...]
            entity_labels = [item["label"] for item in entity_labels_data]
        else:
            # 简单列表格式：["Person", "Role", ...]
            entity_labels = entity_labels_data
    else:
        entity_labels = entity_labels_data

    if isinstance(relation_labels_data, list) and len(relation_labels_data) > 0:
        if isinstance(relation_labels_data[0], dict):
            # 字典列表格式：[{"label": "prj:has_prop", "desc": "..."}, ...]
            relation_labels = [item["label"] for item in relation_labels_data]
        else:
            # 简单列表格式：["prj:has_prop", ...]
            relation_labels = relation_labels_data
    else:
        relation_labels = relation_labels_data

    # 创建标签到ID的映射（1-based索引）
    entity_id_map = {label: idx + 1 for idx, label in enumerate(entity_labels)}
    relation_id_map = {label: idx + 1 for idx, label in enumerate(relation_labels)}

    return entity_id_map, relation_id_map

def train_entity_relation_model(
    train_dataset_path="datasets/my_roberta_v2_traindata_entity_relation_only0724.json",
    entity_labels_file="datasets/my_roberta_entity_labels.json",
    relation_labels_file="datasets/my_roberta_relation_labels.json",
    model_name="dl_models/hfl/chinese-roberta-wwm-ext",
    output_dir=None,
    num_train_epochs=15,  # 增加训练轮数以匹配原始模型的训练强度
    per_device_train_batch_size=1,  # 与原始模型保持一致
    per_device_eval_batch_size=4,   # 与原始模型保持一致
    gradient_accumulation_steps=4,  # 添加梯度累积
    learning_rate=3e-5,             # 与原始模型保持一致
    weight_decay=0.01,
    warmup_ratio=0.1,               # 使用warmup_ratio而不是warmup_steps
    logging_steps=50,               # 更频繁的日志输出
    save_steps=None,                # 动态计算
    eval_steps=None,                # 动态计算
    max_length=256,
    train_split_ratio=0.8
):
    """
    训练专注于Entity和Relation的双任务模型
    """

    # 生成模型保存路径
    if output_dir is None:
        output_dir = generate_model_save_path("roberta_entity_relation")

    print(f"模型将保存到: {output_dir}")

    # 加载标签映射
    entity_id_map, relation_id_map = load_labels_fromfile(entity_labels_file, relation_labels_file)

    # 设置全局变量（为了兼容现有代码）
    global CONVERSATION_ENTITY_ID_MAP, CONVERSATION_RELATION_ID_MAP
    CONVERSATION_ENTITY_ID_MAP = entity_id_map
    CONVERSATION_RELATION_ID_MAP = relation_id_map

    print(f"实体标签数量: {len(entity_id_map)}")
    print(f"关系标签数量: {len(relation_id_map)}")

    # 加载数据集
    with open(train_dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)

    print(f"数据集大小: {len(dataset)}")

    # 分割训练和验证集
    split_idx = int(len(dataset) * train_split_ratio)
    train_data = dataset[:split_idx]
    eval_data = dataset[split_idx:]

    print(f"训练集大小: {len(train_data)}")
    print(f"验证集大小: {len(eval_data)}")

    # 计算动态训练参数（与原始模型保持一致）
    effective_train_size = len(train_data)
    effective_batch_size = per_device_train_batch_size * gradient_accumulation_steps
    steps_per_epoch = effective_train_size // effective_batch_size
    total_steps = steps_per_epoch * num_train_epochs

    # 动态计算save_steps和eval_steps（与原始模型保持一致）
    if eval_steps is None:
        eval_steps = max(100, steps_per_epoch // 3)  # 每三分之一epoch评估一次，更频繁的评估
    if save_steps is None:
        # save_steps必须是eval_steps的整数倍
        save_steps = max(eval_steps * 2, 200)  # 至少是eval_steps的2倍

    print(f"\n📊 训练配置详情:")
    print(f"  • 有效批次大小: {effective_batch_size} (batch_size={per_device_train_batch_size} × accumulation={gradient_accumulation_steps})")
    print(f"  • 每轮步数: {steps_per_epoch}")
    print(f"  • 总训练步数: {total_steps}")
    print(f"  • 评估频率: 每 {eval_steps} 步")
    print(f"  • 保存频率: 每 {save_steps} 步")
    print(f"  • 学习率: {learning_rate}")
    print(f"  • 预热比例: {warmup_ratio}")

    # 初始化tokenizer
    tokenizer = BertTokenizerFast.from_pretrained(model_name)

    # 预处理数据
    print("预处理训练数据...")
    train_processed = preprocess_dataset(train_data, tokenizer, max_length=max_length,
                                       entity_id_map=entity_id_map, relation_id_map=relation_id_map)

    print("预处理验证数据...")
    eval_processed = preprocess_dataset(eval_data, tokenizer, max_length=max_length,
                                      entity_id_map=entity_id_map, relation_id_map=relation_id_map)

    # 创建Dataset对象
    train_dataset = Dataset.from_dict(train_processed)
    eval_dataset = Dataset.from_dict(eval_processed)

    # 初始化模型
    config = BertConfig.from_pretrained(model_name)
    model = RoBERTaForEntityRelation(config,
                                   entity_label_count=len(entity_id_map),
                                   relation_label_count=len(relation_id_map))

    # 加载预训练权重
    bert_model = BertModel.from_pretrained(model_name)
    model.bert.load_state_dict(bert_model.state_dict())

    # 设置训练参数（与原始模型保持一致）
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=num_train_epochs,
        max_steps=total_steps,  # 添加max_steps控制
        per_device_train_batch_size=per_device_train_batch_size,
        per_device_eval_batch_size=per_device_eval_batch_size,
        gradient_accumulation_steps=gradient_accumulation_steps,  # 添加梯度累积
        learning_rate=learning_rate,
        weight_decay=weight_decay,
        warmup_ratio=warmup_ratio,  # 使用warmup_ratio
        logging_steps=logging_steps,
        save_steps=save_steps,
        eval_steps=eval_steps,
        eval_strategy="steps",
        save_strategy="steps",
        load_best_model_at_end=True,
        metric_for_best_model="eval_relation_f1",
        greater_is_better=True,
        report_to=None,
        dataloader_pin_memory=False,
        logging_dir=f'{output_dir}/logs',  # 添加日志目录
        save_total_limit=2,  # 限制保存的检查点数量
        overwrite_output_dir=True,  # 覆盖输出目录
    )

    # 创建自定义的compute_metrics函数，确保输出详细的评估信息
    def compute_metrics_with_logging(eval_pred):
        """带有详细日志输出的评估函数"""
        metrics = compute_metrics(eval_pred)  # 直接调用独立的compute_metrics函数

        # 打印详细的评估结果
        print(f"\n📊 评估结果:")
        for metric_name, metric_value in metrics.items():
            print(f"  • {metric_name}: {metric_value:.4f}")

        return metrics

    # 初始化Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        compute_metrics=compute_metrics_with_logging,
        data_collator=default_data_collator,
    )

    # 开始训练
    print("\n🚀 开始训练...")
    print("=" * 80)
    trainer.train()
    print("=" * 80)
    print("✅ 训练完成!")

    # 保存最终模型
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)

    # 保存标签映射
    with open(os.path.join(output_dir, "entity_labels.json"), 'w', encoding='utf-8') as f:
        json.dump(entity_id_map, f, ensure_ascii=False, indent=2)

    with open(os.path.join(output_dir, "relation_labels.json"), 'w', encoding='utf-8') as f:
        json.dump(relation_id_map, f, ensure_ascii=False, indent=2)

    print(f"训练完成！模型已保存到: {output_dir}")

    return model, tokenizer, output_dir

def test_model(model_path, test_sentences=None):
    """
    测试训练好的双任务模型
    支持新版本transformers的model.safetensors格式
    """
    if test_sentences is None:
        test_sentences = [
            "项目P-19298374的服务器在哪个柜子上",
            "项目P-92039482的服务器和保护都在1#箱子里吗？",
            "流程审批还剩哪些环节？"
        ]

    print(f"正在加载模型: {model_path}")

    # 加载模型和tokenizer
    tokenizer = BertTokenizerFast.from_pretrained(model_path)
    config = BertConfig.from_pretrained(model_path)

    # 加载标签映射
    with open(os.path.join(model_path, "entity_labels.json"), 'r', encoding='utf-8') as f:
        entity_id_map = json.load(f)

    with open(os.path.join(model_path, "relation_labels.json"), 'r', encoding='utf-8') as f:
        relation_id_map = json.load(f)

    print(f"加载标签映射 - 实体: {len(entity_id_map)}个, 关系: {len(relation_id_map)}个")

    # 设置全局变量
    global CONVERSATION_ENTITY_ID_MAP, CONVERSATION_RELATION_ID_MAP
    CONVERSATION_ENTITY_ID_MAP = entity_id_map
    CONVERSATION_RELATION_ID_MAP = relation_id_map

    # 初始化模型
    model = RoBERTaForEntityRelation(config,
                                   entity_label_count=len(entity_id_map),
                                   relation_label_count=len(relation_id_map))

    # 尝试加载训练好的权重 - 支持多种格式
    model_file_candidates = [
        "model.safetensors",  # 新版本格式
        "pytorch_model.bin",  # 旧版本格式
        "model.bin"          # 备选格式
    ]

    model_loaded = False
    for model_file in model_file_candidates:
        model_file_path = os.path.join(model_path, model_file)
        if os.path.exists(model_file_path):
            try:
                if model_file.endswith('.safetensors'):
                    # 使用transformers的安全加载方式
                    from transformers import AutoModel
                    temp_model = AutoModel.from_pretrained(model_path)
                    # 只加载BERT部分的权重
                    model.bert.load_state_dict(temp_model.state_dict(), strict=False)
                    print(f"✓ 成功加载模型权重: {model_file}")
                    model_loaded = True
                    break
                else:
                    # 传统的pytorch加载方式
                    state_dict = torch.load(model_file_path, map_location='cpu')
                    model.load_state_dict(state_dict)
                    print(f"✓ 成功加载模型权重: {model_file}")
                    model_loaded = True
                    break
            except Exception as e:
                print(f"⚠️ 加载 {model_file} 失败: {e}")
                continue

    if not model_loaded:
        print("⚠️ 警告: 无法加载预训练权重，使用随机初始化的模型进行测试")

    # 进行推理
    print("开始推理测试...")
    results = inference(model, test_sentences, tokenizer,
                       entity_id_map=entity_id_map, relation_id_map=relation_id_map)

    # 打印结果
    for i, result in enumerate(results):
        print(f"\n=== 测试句子 {i+1} ===")
        print(f"句子: {result['sentence']}")
        print(f"实体数量: {len(result['entities'])}")
        if result['entities']:
            for j, entity in enumerate(result['entities'][:3]):  # 只显示前3个实体
                print(f"  实体{j+1}: {entity}")
        print(f"关系数量: {len(result['relations'])}")
        if result['relations']:
            for j, relation in enumerate(result['relations'][:3]):  # 只显示前3个关系
                print(f"  关系{j+1}: {relation}")

    return results

def main():
    """
    主程序：提供简单的命令行界面
    """
    print("=== RoBERTa Entity-Relation 双任务模型 ===")
    print("专注于实体提取和关系提取的优化版本")

    while True:
        print("\n请选择操作:")
        print("1. 训练新模型")
        print("2. 测试现有模型")
        print("3. 数据集分析")
        print("4. 退出")

        choice = input("请输入选项 (1-4): ").strip()

        if choice == '1':  # 训练新模型
            print("\n开始训练新的双任务模型...")
            try:
                model, tokenizer, output_dir = train_entity_relation_model()
                print(f"训练完成！模型保存在: {output_dir}")

                # 询问是否立即测试
                test_choice = input("是否立即测试模型？(y/n): ")
                if test_choice.lower() == 'y':
                    test_model(output_dir)

            except Exception as e:
                print(f"❌ 训练过程中出现错误: {e}")
                print(f"🔍 错误类型: {type(e).__name__}")
                import traceback
                print("📋 详细错误信息:")
                traceback.print_exc()

        elif choice == '2':  # 测试现有模型
            model_path = input("请输入模型路径: ")
            if os.path.exists(model_path):
                try:
                    test_model(model_path)
                except Exception as e:
                    print(f"测试过程中出现错误: {e}")
            else:
                print("模型路径不存在！")

        elif choice == '3':  # 数据集分析
            print("\n运行数据集分析...")
            try:
                os.system("python analyze_dataset.py")
            except Exception as e:
                print(f"分析过程中出现错误: {e}")

        elif choice == '4':  # 退出
            print("退出程序")
            break

        else:
            print("无效选项，请重新选择")

if __name__ == "__main__":
    main()
