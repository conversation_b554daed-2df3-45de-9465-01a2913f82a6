from sklearn.metrics import accuracy_score, precision_recall_fscore_support, f1_score,confusion_matrix
import torch
import torch.nn as nn
from transformers import BertModel, Bert<PERSON>onfig, Bert<PERSON>okenizer, BertTokenizerFast, BertPreTrainedModel, BertForQuestionAnswering
from transformers import Trainer, TrainingArguments
from transformers import default_data_collator
import json
import os,sys 
import numpy as np
from datetime import datetime
from simple_term_menu import TerminalMenu

# 当前根目录
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
# 添加根目录到系统路径
sys.path.append(ROOT_DIR)
from datasets import load_dataset, Dataset, concatenate_datasets

# 支持自定义模型保存路径
def generate_model_save_path(custom_name=None, base_dir="./my_models"):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if custom_name is None:
        custom_name = "roberta_model"

    # 清理自定义名称，移除不安全的字符
    safe_name = "".join(c for c in custom_name if c.isalnum() or c in ('-', '_')).strip()
    if not safe_name:
        safe_name = "roberta_model"

    model_dir_name = f"{safe_name}_{timestamp}"
    save_path = os.path.join(base_dir, model_dir_name)
    return save_path

class RoBERTaForConversationClassification(BertPreTrainedModel):

    # when init from scratch, means load base roberta from file
    def __init__(self, config, grammar_label_count=None, subdomain_label_count=None):
        # call parent init
        super().__init__(config)
        # core backbone model
        # did not need load config mannually, cause it will automatically load config from pretrained_model_path
        self.config = config
        # base roberta model
        self.bert = BertModel(config) 
        # dropout layer
        classifier_dropout = (
            config.classifier_dropout if config.classifier_dropout is not None else config.hidden_dropout_prob
        )
        self.dropout = nn.Dropout(classifier_dropout)

        # 确定各类标签的数量，支持参数传入或使用全局变量
        if grammar_label_count is not None:
            num_grammar_labels = grammar_label_count
        else:
            try:
                num_grammar_labels = len(CONVERSATION_GRAMMAR_ID_MAP)
            except NameError:
                num_grammar_labels = 3  # 默认语法标签数量
                print(f"Warning: CONVERSATION_GRAMMAR_ID_MAP not defined, using default: {num_grammar_labels}")

        if subdomain_label_count is not None:
            num_subdomain_labels = subdomain_label_count
        else:
            try:
                num_subdomain_labels = len(CONVERSATION_SUBDOMAIN_ID_MAP)
            except NameError:
                num_subdomain_labels = 4  # 默认子域标签数量
                print(f"Warning: CONVERSATION_SUBDOMAIN_ID_MAP not defined, using default: {num_subdomain_labels}")



        # multi-label, because a qustion can have judge and reason at same time
        self.question_grammar_classifier = nn.Linear(config.hidden_size, num_grammar_labels)
        # single-label, because projcet sub-domain can only be one
        self.question_subdomain_classifier = nn.Linear(config.hidden_size, num_subdomain_labels)

        # ===== 损失权重配置 =====
        # 专注于语法和子域分类任务的权重配置
        self.weight_grammar = 1.0      # 语法分类权重
        self.weight_subdomain = 1.0    # 子域分类权重

        # token classification labels
        # save conversation history and extracted key entities and properties
        # window size = 5
        self.conversation_history = []
        self.conversation_history_size = 3*3
        self.project_state = {} # upate based on conversation history
        self.project_state_size = 3
        # customized attention decoder - prompt user to provide missing key entities

        # Initialize weights and apply final processing
        self.post_init()


    def forward(self,
                input_ids=None,
                attention_mask=None,
                token_type_ids=None,
                grammar_labels=None,
                subdomain_labels=None):
        
        batch_size = input_ids.size(0) if input_ids is not None else 1

        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask, token_type_ids=token_type_ids)
        sequence_output = outputs.last_hidden_state # [batch, seq_len, hidden]
        
        # Intent classification (CLS token)
        cls_token_output = outputs.last_hidden_state[:, 0, :]
        
        # Intent classification (CLS token)
        grammar_logits = self.question_grammar_classifier(cls_token_output)
        subdomain_logits = self.question_subdomain_classifier(cls_token_output)

        loss_dict = {}
        # grammar loss
        if grammar_labels is not None:
            grammar_loss = torch.nn.BCEWithLogitsLoss()(grammar_logits, grammar_labels.float())
            loss_dict["grammar_loss"] = grammar_loss
        # subdomain loss
        if subdomain_labels is not None:
            subdomain_loss = torch.nn.CrossEntropyLoss()(subdomain_logits.view(-1, subdomain_logits.size(-1)), subdomain_labels.view(-1).long())
            loss_dict["subdomain_loss"] = subdomain_loss



        # Use instance variables for loss weights (supports staged training)
        # sum total loss on weighted average
        total_loss = sum([
            self.weight_grammar * loss_dict.get('grammar_loss', 0.0),
            self.weight_subdomain * loss_dict.get('subdomain_loss', 0.0),
        ])

        # # 在 forward 方法中添加损失检查
        # if total_loss < 1e-5:
        #     print("Warning: Loss is suspiciously low, possible numerical issues")
        #     print(f"Loss components: Grammar={loss_dict.get('grammar_loss', 0.0)}, "
        #           f"Subdomain={loss_dict.get('subdomain_loss', 0.0)}")

        # Returning both predictions as a tuple
        return total_loss, {
            "grammar_logits": grammar_logits,
            "subdomain_logits": subdomain_logits,
            "sequence_output": sequence_output # for inference convinence use
        }

    @staticmethod
    def compute_metrics(eval_pred, grammar_threshold=0.5, debug=False):
        """
        计算评估指标

        Args:
            eval_pred: (preds, labels) 元组
            grammar_threshold: grammar分类的阈值
            debug: 是否输出调试信息
        """
        try:
            preds, labels = eval_pred

            if debug:
                print(f"\n🔍 Debug - compute_metrics 输入分析:")
                print(f"  - preds keys: {list(preds.keys()) if isinstance(preds, dict) else type(preds)}")
                print(f"  - labels type: {type(labels)}")
                print(f"  - labels length: {len(labels) if hasattr(labels, '__len__') else 'N/A'}")

            # CAUSION:
            # labels here is a list of list, not dict of list, i don't know why
            # the sequence of the list is same as after processed by preprocess_dataset
            # reconstruct the labels with column names as dict
            labels = {
                'grammar_labels': labels[0],
                'subdomain_labels': labels[1],
            }

            if debug:
                print(f"  - grammar_labels type: {type(labels['grammar_labels'])}")
                print(f"  - subdomain_labels type: {type(labels['subdomain_labels'])}")

            # 1. INTENT: Multi-label grammar (e.g., ['Query', 'Reason'])
            pred_grammar_logits = preds['grammar_logits']
            true_grammar = labels['grammar_labels']
            pred_grammar = (torch.sigmoid(torch.as_tensor(pred_grammar_logits)) > grammar_threshold).int().numpy()
            true_grammar = torch.as_tensor(true_grammar).int().numpy()

            if debug:
                print(f"  - pred_grammar shape: {pred_grammar.shape}")
                print(f"  - true_grammar shape: {true_grammar.shape}")
                print(f"  - grammar threshold: {grammar_threshold}")

                # 统计grammar标签分布
                from collections import Counter
                pred_grammar_labels = [i for sample in pred_grammar for i, val in enumerate(sample) if val == 1]
                true_grammar_labels = [i for sample in true_grammar for i, val in enumerate(sample) if val == 1]
                print(f"  - pred grammar label distribution: {Counter(pred_grammar_labels)}")
                print(f"  - true grammar label distribution: {Counter(true_grammar_labels)}")

            grammar_f1, _ = precision_recall_fscore_support(true_grammar, pred_grammar, average='samples', zero_division=0)[:2]

            # 2. SUBDOMAIN: Single-label intent
            pred_subdomain_logits = preds['subdomain_logits']
            true_subdomain = labels['subdomain_labels']
            pred_subdomain = torch.argmax(torch.as_tensor(pred_subdomain_logits), dim=-1).numpy()
            true_subdomain = torch.as_tensor(true_subdomain).numpy()

            if debug:
                print(f"  - pred_subdomain shape: {pred_subdomain.shape}")
                print(f"  - true_subdomain shape: {true_subdomain.shape}")

                # 统计subdomain标签分布
                from collections import Counter
                print(f"  - pred subdomain label distribution: {Counter(pred_subdomain)}")
                print(f"  - true subdomain label distribution: {Counter(true_subdomain)}")

            subdomain_acc = accuracy_score(true_subdomain, pred_subdomain)

            if debug:
                print(f"🎯 Final metrics: grammar_f1={grammar_f1:.4f}, subdomain_acc={subdomain_acc:.4f}")

            return {
                'grammar_f1': grammar_f1,
                'subdomain_acc': subdomain_acc,
            }

        except Exception as e:
            print(f"❌ Error in compute_metrics: {e}")
            import traceback
            traceback.print_exc()
            return {
                'grammar_f1': 0.0,
                'subdomain_acc': 0.0,
            }
        
    def inference(self, sentences, tokenizer, max_length=256, padding="max_length", truncation=True, grammar_threshold=0.5):

        # tokenize sentences
        inputs = tokenizer(sentences, padding=padding, truncation=truncation, max_length=max_length, return_tensors="pt")

        # move to gpu if available
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.to(device)
        inputs = {key: value.to(device) for key, value in inputs.items()}

        # disable gradient for inference
        with torch.no_grad():
            _, outputs = self.forward(
                input_ids=inputs.get("input_ids"),
                attention_mask=inputs.get("attention_mask"),
                token_type_ids=inputs.get("token_type_ids", None)  # Optional argument
            )

        grammar_logits = outputs["grammar_logits"]
        subdomain_logits = outputs["subdomain_logits"]

        results = []
        for b_idx, sentence in enumerate(sentences):

            # grammar classification multi-label
            grammar_pred = (torch.sigmoid(grammar_logits[b_idx]) > grammar_threshold).int()
            grammar_intent_label = [CONVERSATION_ID_GRAMMAR_ARY[i] for i, val in enumerate(grammar_pred) if val == 1]

            # subdomain classification single-label
            subdomain_pred = torch.argmax(subdomain_logits[b_idx]).item()
            # 修复索引错误：subdomain_pred是从0开始的索引，直接使用
            if subdomain_pred >= len(CONVERSATION_ID_SUBDOMAIN_ARY):
                print(f"Warning: Invalid subdomain index {subdomain_pred}, using first available")
                subdomain_pred = 0
            subdomain_intent_label = CONVERSATION_ID_SUBDOMAIN_ARY[subdomain_pred]

            results.append({
                "sentence": sentence,
                "grammar": grammar_intent_label,
                "subdomain": subdomain_intent_label,
            })

        return results

    def print_training_config(self, training_args, train_dataset, eval_dataset, max_row_count=None):
        """
        打印详细的训练配置信息

        Args:
            training_args: TrainingArguments对象
            train_dataset: 训练数据集
            eval_dataset: 验证数据集
            max_row_count: 最大训练样本数
        """
        from datetime import datetime

        print("\n" + "="*80)
        print("🚀 训练配置详情")
        print("="*80)
        print(f"⏰ 配置时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 1. 训练参数
        print(f"\n📊 训练参数:")
        print(f"  • 学习率 (learning_rate): {training_args.learning_rate}")
        print(f"  • 训练轮数 (num_train_epochs): {training_args.num_train_epochs}")
        print(f"  • 最大训练步数 (max_steps): {training_args.max_steps}")
        print(f"  • 权重衰减 (weight_decay): {training_args.weight_decay}")
        print(f"  • 预热比例 (warmup_ratio): {training_args.warmup_ratio}")
        print(f"  • 学习率调度器 (lr_scheduler_type): {training_args.lr_scheduler_type}")

        # 2. 批次和梯度参数
        print(f"\n🔄 批次和梯度参数:")
        print(f"  • 训练批次大小 (per_device_train_batch_size): {training_args.per_device_train_batch_size}")
        print(f"  • 验证批次大小 (per_device_eval_batch_size): {training_args.per_device_eval_batch_size}")
        print(f"  • 梯度累积步数 (gradient_accumulation_steps): {training_args.gradient_accumulation_steps}")
        print(f"  • 有效批次大小: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps}")
        print(f"  • 最大梯度范数 (max_grad_norm): {training_args.max_grad_norm}")

        # 3. 保存和评估参数
        print(f"\n💾 保存和评估参数:")
        print(f"  • 保存步数 (save_steps): {training_args.save_steps}")
        print(f"  • 评估步数 (eval_steps): {training_args.eval_steps}")
        print(f"  • 保存策略 (save_strategy): {training_args.save_strategy}")
        print(f"  • 评估策略 (evaluation_strategy): {training_args.evaluation_strategy}")
        print(f"  • 保存总限制 (save_total_limit): {training_args.save_total_limit}")
        print(f"  • 加载最佳模型 (load_best_model_at_end): {training_args.load_best_model_at_end}")

        # 4. 数据集信息
        print(f"\n📚 数据集信息:")
        train_size = len(train_dataset) if train_dataset else 0
        eval_size = len(eval_dataset) if eval_dataset else 0
        effective_train_size = min(max_row_count, train_size) if max_row_count else train_size

        print(f"  • 训练集总大小: {train_size:,} 样本")
        print(f"  • 有效训练大小: {effective_train_size:,} 样本")
        print(f"  • 验证集大小: {eval_size:,} 样本")
        print(f"  • 训练/验证比例: {effective_train_size/eval_size:.2f}:1" if eval_size > 0 else "  • 训练/验证比例: N/A")

        # 5. 模型参数信息
        print(f"\n🧠 模型参数信息:")
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        print(f"  • 总参数数量: {total_params:,}")
        print(f"  • 可训练参数: {trainable_params:,}")
        print(f"  • 冻结参数: {total_params - trainable_params:,}")
        print(f"  • 参数利用率: {trainable_params/total_params*100:.1f}%")

        # 6. 多任务损失权重
        print(f"\n⚖️ 多任务损失权重:")
        print(f"  • Grammar权重: {getattr(self, 'weight_grammar', 1.0)}")
        print(f"  • Subdomain权重: {getattr(self, 'weight_subdomain', 1.0)}")
        print(f"  • Position权重: {getattr(self, 'weight_position', 1.0)}")
        print(f"  • Entity Type权重: {getattr(self, 'weight_entity_type', 1.0)}")
        print(f"  • Relation权重: {getattr(self, 'weight_relation', 1.0)}")

        # 7. 硬件和环境信息
        print(f"\n💻 硬件和环境信息:")
        import torch
        print(f"  • 设备: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
        if torch.cuda.is_available():
            print(f"  • GPU数量: {torch.cuda.device_count()}")
            print(f"  • 当前GPU: {torch.cuda.get_device_name()}")
            print(f"  • GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print(f"  • PyTorch版本: {torch.__version__}")

        # 8. 计算训练步数和时间估算
        print(f"\n⏱️ 训练步数和时间估算:")
        steps_per_epoch = effective_train_size // (training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps)
        total_steps = int(steps_per_epoch * training_args.num_train_epochs)
        eval_frequency = training_args.eval_steps if training_args.eval_steps else steps_per_epoch

        print(f"  • 每轮步数: {steps_per_epoch:,}")
        print(f"  • 总训练步数: {total_steps:,}")
        print(f"  • 评估频率: 每 {eval_frequency} 步")
        print(f"  • 保存频率: 每 {training_args.save_steps} 步")

        print("="*80)
        print("🎯 训练即将开始...")
        print("="*80 + "\n")

    # prepare dataset for training
    # sample data format
    """
    {
        "sentence": "项目 XT-20250101-1234 的负责人是谁？",
        "grammar_labels": ["Query", "Reason"],  // multi-label
        "subdomain_label": "BaseInfo",          // single-label
        "entity_spans": [
            { "start": 3, "end": 16, "type": "B-PROJID" },  // char positions
            { "start": 17, "end": 20, "type": "B-ROLE-MGR" }
        ],
        "relation_pairs": [
            { "start_i": 3, "end_i": 16, "start_j": 17, "end_j": 20, "type": "Has_Property" }
        ]
    }
    """
    def preprocess_dataset(dataset, tokenizer, max_length=256, padding="max_length", truncation=True,
                          grammar_id_map=None, subdomain_id_map=None):
        """
        预处理数据集，支持传入标签映射或使用全局变量

        Args:
            dataset: 输入数据集
            tokenizer: 分词器
            max_length: 最大序列长度
            padding: 填充策略
            truncation: 是否截断
            grammar_id_map: 语法标签映射，如果为None则使用全局变量
            subdomain_id_map: 子域标签映射，如果为None则使用全局变量
        """

        # 获取标签映射，优先使用参数，否则使用全局变量
        if grammar_id_map is not None:
            GRAMMAR_MAP = grammar_id_map
        else:
            try:
                GRAMMAR_MAP = CONVERSATION_GRAMMAR_ID_MAP
            except NameError:
                raise ValueError("grammar_id_map parameter is required when CONVERSATION_GRAMMAR_ID_MAP is not defined")

        if subdomain_id_map is not None:
            SUBDOMAIN_MAP = subdomain_id_map
        else:
            try:
                SUBDOMAIN_MAP = CONVERSATION_SUBDOMAIN_ID_MAP
            except NameError:
                raise ValueError("subdomain_id_map parameter is required when CONVERSATION_SUBDOMAIN_ID_MAP is not defined")



        # Handle both batched and non-batched inputs
        batched = isinstance(dataset["sentence"], list)
        
        # For non-batched case, convert to batch of size 1
        if not batched:
            batch_sentences = [dataset["sentence"]]
            batch_grammar_labels = [dataset["grammar_labels"]]
            batch_subdomain_label = [dataset["subdomain_label"]]
        else:
            batch_sentences = dataset["sentence"]
            batch_grammar_labels = dataset["grammar_labels"]
            batch_subdomain_label = dataset["subdomain_label"]
        
        if not batch_sentences:
            return {}
        
        # Tokenize the sentences
        encodings = tokenizer(batch_sentences, padding=padding, truncation=truncation, 
                             max_length=max_length, return_offsets_mapping=True)
        
        batch_input_ids = encodings['input_ids']
        batch_attention_mask = encodings['attention_mask']
        batch_offset_mappings = encodings['offset_mapping']
        
        batch_size = len(batch_sentences)
        
        grammar_labels_batch = []
        subdomain_labels_batch = []
        
        for i in range(batch_size):
            sentence = batch_sentences[i]
            offset_mapping = batch_offset_mappings[i]
            seq_len = len(batch_input_ids[i])
            
            # Multi-hot grammar labels
            grammar_vector = torch.zeros(len(GRAMMAR_MAP))
            valid_labels = 0
            for label in batch_grammar_labels[i]:
                grammar_id = GRAMMAR_MAP.get(label, None)
                # 修复：现在grammar_id直接是0-based索引，无需转换
                if grammar_id is not None and 0 <= grammar_id < len(grammar_vector):
                    grammar_vector[grammar_id] = 1
                    valid_labels += 1

            # 严格验证：如果没有有效标签，报错而不是使用默认值
            if valid_labels == 0:
                raise ValueError(f"No valid grammar labels found for sentence: '{sentence}'. "
                               f"Available labels: {list(GRAMMAR_MAP.keys())}. "
                               f"Provided labels: {batch_grammar_labels[i]}")
    
            # Single subdomain label - 严格验证
            subdomain_label_id = SUBDOMAIN_MAP.get(batch_subdomain_label[i], None)
            if subdomain_label_id is None:
                raise ValueError(f"Invalid subdomain label '{batch_subdomain_label[i]}' for sentence: '{sentence}'. "
                               f"Available labels: {list(SUBDOMAIN_MAP.keys())}")

            # 修复：现在subdomain_label_id直接是0-based索引，无需转换
            # 直接使用0-based索引，与CrossEntropyLoss的期望一致，也与推理时的argmax输出一致
            subdomain_label = subdomain_label_id
            
            grammar_labels_batch.append(grammar_vector)
            subdomain_labels_batch.append(subdomain_label)

        # print batch size
        print("batch size: ", len(batch_sentences))
        print("mapped sentences: \n", "\n".join(batch_sentences[:10]), "\n..." if len(batch_sentences) > 10 else "") 

        # CAUSION:
        # must return custom column name, end with _labels
        # i don't know why, i guess cause the dataset from json file did not have such column name?
        # if not end with _labels, then column will missed in compute_metrics's labels list
        if not batched:
            return {
                'input_ids': batch_input_ids[0],
                'attention_mask': batch_attention_mask[0],
                'grammar_labels': grammar_labels_batch[0],
                'subdomain_labels': subdomain_labels_batch[0],
            }
        else:
            return {
                'input_ids': batch_input_ids,
                'attention_mask': batch_attention_mask,
                'grammar_labels': grammar_labels_batch,
                'subdomain_labels': subdomain_labels_batch,
            }

    # when train datset if load in stream mode, max_row_count must be specified
    def custom_train(self, tokenizer, train_dataset, eval_dataset, max_row_count=None, custom_epoch=3, save_steps=500,
                    auto_save=True, custom_name=None, learning_rate=0.000030, per_device_train_batch_size=4,
                    gradient_accumulation_steps=8, warmup_ratio=0.1, weight_decay=0.01, lr_scheduler_type="linear",
                    weight_grammar=1.0, weight_subdomain=1.0):

        if max_row_count is None:
            max_row_count = len(train_dataset)

        # 应用多任务权重
        self.update_loss_weights(
            weight_grammar=weight_grammar,
            weight_subdomain=weight_subdomain,
        )

        print("train dataset size: ", max_row_count)
        print("eval dataset size: ", len(eval_dataset) if eval_dataset else 0)
        print("train dataset: ", train_dataset)
        print("eval dataset: ", eval_dataset)
        print(f"Applied multitask weights - Grammar: {weight_grammar}, Subdomain: {weight_subdomain}")

        tokenized_train_dataset = train_dataset.map(lambda x: RoBERTaForConversationClassification.preprocess_dataset(x, tokenizer), batched=True)
        # convert to torch tensor
        tokenized_train_dataset.set_format(type='torch')
        if eval_dataset is not None:
            tokenized_eval_dataset = eval_dataset.map(lambda x: RoBERTaForConversationClassification.preprocess_dataset(x, tokenizer), batched=True)
            # convert to torch tensor
            tokenized_eval_dataset.set_format(type='torch')
            print(tokenized_eval_dataset[0].keys())
        else:
            tokenized_eval_dataset = None

        training_args = TrainingArguments(
            max_steps=max_row_count*custom_epoch,
            output_dir="./results",             # 保存模型的目录
            overwrite_output_dir=True,          # 是否覆盖输出目录
            save_strategy="no",                 # 保存策略
            save_steps=save_steps,              # 每500步保存一次
            save_total_limit=1,                 # 限制保存的模型数量
            eval_strategy="steps" if eval_dataset else "no",              # 每个epoch后进行评估
            eval_steps=max_row_count // 2,      # 每半个epoch评估一次
            per_device_train_batch_size=per_device_train_batch_size,      # 使用优化后的批量大小
            per_device_eval_batch_size=4,       # 评估时的批量大小
            gradient_accumulation_steps=gradient_accumulation_steps,      # 使用优化后的梯度累积步数
            num_train_epochs=custom_epoch,      # 使用传入的训练轮数
            logging_dir='./logs',               # 日志目录
            logging_steps=100,                  # 每100步输出日志
            #fp16=True,
            warmup_ratio=warmup_ratio,          # 使用优化后的预热比例
            lr_scheduler_type=lr_scheduler_type, # 使用优化后的学习率调度器
            weight_decay=weight_decay,          # 使用优化后的权重衰减
            learning_rate=learning_rate,        # 使用优化后的学习率
            report_to="tensorboard"             # 输出到tensorboard
        )
        trainer = Trainer(
            model=self,
            args=training_args,
            train_dataset=tokenized_train_dataset,
            eval_dataset=tokenized_eval_dataset if tokenized_eval_dataset else None,
            # no need to use custom data collator, cause we already padding to fixed size
            #data_collator=self.custom_data_collator,
            processing_class=tokenizer,
            compute_metrics=RoBERTaForConversationClassification.compute_metrics if tokenized_eval_dataset else None  # Removed eval_dataset parameter
        )

        # 打印详细的训练配置信息
        self.print_training_config(
            training_args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            max_row_count=max_row_count
        )

        trainer.train()
        print("train done.")

        # 自动保存训练后的模型
        if auto_save:
            print("\n" + "="*50)
            print("训练完成，正在保存模型...")
            print("="*50)

            # 生成保存路径
            if custom_name is None:
                custom_name = "custom_trained_model"

            save_path = generate_model_save_path(custom_name)
            os.makedirs(save_path, exist_ok=True)

            try:
                # 保存模型和tokenizer
                self.save_pretrained(save_path)
                tokenizer.save_pretrained(save_path)

                print(f"✅ 模型自动保存成功!")
                print(f"📁 保存路径: {save_path}")
                print(f"📝 模型名称: {custom_name}")
                print(f"🕒 保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                # 显示保存的文件
                saved_files = os.listdir(save_path)
                print(f"📄 保存的文件: {', '.join(saved_files)}")

            except Exception as e:
                print(f"❌ 自动保存模型时出错: {e}")
                print("请手动使用选项7保存模型")

    def update_loss_weights(self, weight_grammar, weight_subdomain):
        """Update loss weights for staged training"""
        self.weight_grammar = weight_grammar
        self.weight_subdomain = weight_subdomain
        print(f"Updated loss weights - Grammar: {weight_grammar}, Subdomain: {weight_subdomain}")

    def train_one_stage(self, tokenizer, train_dataset, eval_dataset, epochs, stage_name,
                       max_row_count=None, save_intermediate=True, learning_rate=2e-5):
        """Train model for one stage with specific configuration"""

        if max_row_count is None:
            max_row_count = len(train_dataset)

        print(f"\n{'='*60}")
        print(f"Starting {stage_name}")
        print(f"{'='*60}")
        print(f"Training for {epochs} epochs with {max_row_count} samples")
        print(f"Current loss weights - Grammar: {self.weight_grammar}, Subdomain: {self.weight_subdomain}")

        # Preprocess datasets
        tokenized_train_dataset = train_dataset.map(
            lambda x: RoBERTaForConversationClassification.preprocess_dataset(x, tokenizer),
            batched=True
        )
        tokenized_train_dataset.set_format(type='torch')

        if eval_dataset is not None:
            tokenized_eval_dataset = eval_dataset.map(
                lambda x: RoBERTaForConversationClassification.preprocess_dataset(x, tokenizer),
                batched=True
            )
            tokenized_eval_dataset.set_format(type='torch')
        else:
            tokenized_eval_dataset = None

        # Configure training arguments for this stage
        output_dir = f"./results/{stage_name.lower().replace(' ', '_')}"
        training_args = TrainingArguments(
            max_steps=max_row_count * epochs,
            output_dir=output_dir,
            overwrite_output_dir=True,
            save_strategy="epoch" if save_intermediate else "no",
            save_steps=max_row_count if save_intermediate else 1000,
            save_total_limit=2,
            eval_strategy="epoch" if eval_dataset else "no",
            per_device_train_batch_size=1,
            per_device_eval_batch_size=4,
            gradient_accumulation_steps=4,
            num_train_epochs=1,  # We control epochs via max_steps
            logging_dir=f'./logs/{stage_name.lower().replace(" ", "_")}',
            logging_steps=50,
            warmup_ratio=0.01,
            lr_scheduler_type="cosine",
            learning_rate=learning_rate,
            report_to="tensorboard"
        )

        trainer = Trainer(
            model=self,
            args=training_args,
            train_dataset=tokenized_train_dataset,
            eval_dataset=tokenized_eval_dataset if tokenized_eval_dataset else None,
            processing_class=tokenizer,
            compute_metrics=RoBERTaForConversationClassification.compute_metrics if tokenized_eval_dataset else None
        )

        # 打印详细的训练配置信息（分阶段训练）
        print(f"\n🎯 {stage_name} - 详细配置:")
        self.print_training_config(
            training_args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            max_row_count=max_row_count
        )

        # Train for this stage
        train_result = trainer.train()

        # Save intermediate checkpoint if requested
        if save_intermediate:
            checkpoint_path = f"./my_models/{stage_name.lower().replace(' ', '_')}_checkpoint"
            self.save_pretrained(checkpoint_path)
            tokenizer.save_pretrained(checkpoint_path)
            print(f"Saved intermediate checkpoint to {checkpoint_path}")

        print(f"Completed {stage_name}")
        return train_result

    def staged_training(self, tokenizer, train_dataset, eval_dataset, custom_epoch=5,
                       save_intermediate=True, resume_from_stage=1):
        """
        Train the model using a three-stage progressive approach

        Args:
            tokenizer: The tokenizer to use
            train_dataset: Training dataset
            eval_dataset: Evaluation dataset
            custom_epoch: Total epochs to distribute across stages (default: 5)
            save_intermediate: Whether to save checkpoints after each stage
            resume_from_stage: Stage to resume from (1, 2, or 3)
        """

        if train_dataset is None or eval_dataset is None:
            raise ValueError("Both train_dataset and eval_dataset must be provided for staged training")

        max_row_count = len(train_dataset)
        print(f"\n{'='*80}")
        print(f"STARTING STAGED TRAINING")
        print(f"{'='*80}")
        print(f"Total training samples: {max_row_count}")
        print(f"Total evaluation samples: {len(eval_dataset)}")
        print(f"Total epochs to distribute: {custom_epoch}")
        print(f"Resume from stage: {resume_from_stage}")
        print(f"Save intermediate checkpoints: {save_intermediate}")

        # Store original weights for restoration if needed
        # 修复：使用与方法参数名匹配的键名
        original_weights = {
            'weight_grammar': self.weight_grammar,
            'weight_subdomain': self.weight_subdomain,
            'weight_position': self.weight_position,
            'weight_entity_type': self.weight_entity_type,
            'weight_relation': self.weight_relation
        }

        stage_results = {}

        try:
            # Stage 1: Foundation - Entity boundary detection + grammar classification
            if resume_from_stage <= 1:
                print(f"\n🚀 Preparing Stage 1: Foundation Training")
                self.update_loss_weights(
                    # weight_grammar=2.0, 
                    # weight_subdomain=1.0,
                    # weight_position=3.0,
                    # weight_entity_type=1.0,
                    # weight_relation=0.1
                    weight_grammar=0,
                    weight_subdomain=0,
                    weight_position=0.7,
                    weight_entity_type=0.3,
                    weight_relation=0
                )

                stage1_epochs = max(1, custom_epoch // 3 + 1)  # At least 1 epoch, roughly 1/3 of total
                stage_results['stage1'] = self.train_one_stage(
                    tokenizer=tokenizer,
                    train_dataset=train_dataset,
                    eval_dataset=eval_dataset,
                    epochs=stage1_epochs,
                    stage_name="Stage 1 Foundation",
                    max_row_count=max_row_count,
                    save_intermediate=save_intermediate,
                    learning_rate=3e-5  # Slightly higher LR for foundation
                )

            # Stage 2: Entity Types - Introduce entity type classification
            if resume_from_stage <= 2:
                print(f"\n🎯 Preparing Stage 2: Entity Type Training")
                self.update_loss_weights(
                    # weight_grammar=1.0,
                    # weight_subdomain=0.5,
                    # weight_position=2.0,
                    # weight_entity_type=2.0,
                    # weight_relation=0.3
                    weight_grammar=0,
                    weight_subdomain=0.2,
                    weight_position=0,
                    weight_entity_type=0,
                    weight_relation=0.8
                )

                stage2_epochs = max(1, custom_epoch // 3)  # Roughly 1/3 of total
                stage_results['stage2'] = self.train_one_stage(
                    tokenizer=tokenizer,
                    train_dataset=train_dataset,
                    eval_dataset=eval_dataset,
                    epochs=stage2_epochs,
                    stage_name="Stage 2 Entity Types",
                    max_row_count=max_row_count,
                    save_intermediate=save_intermediate,
                    learning_rate=2e-5  # Standard LR
                )

            # Stage 3: Relations - Full multi-task learning with relation emphasis
            if resume_from_stage <= 3:
                print(f"\n🔗 Preparing Stage 3: Relation Extraction Training")
                self.update_loss_weights(
                    # weight_grammar=1.0,
                    # weight_subdomain=0.5,
                    # weight_position=1.5,
                    # weight_entity_type=1.0,
                    # weight_relation=1.5
                    weight_grammar=1,
                    weight_subdomain=0,
                    weight_position=0,
                    weight_entity_type=0,
                    weight_relation=0
                )

                stage3_epochs = max(1, custom_epoch - (custom_epoch // 3 + 1) - (custom_epoch // 3))  # Remaining epochs
                stage_results['stage3'] = self.train_one_stage(
                    tokenizer=tokenizer,
                    train_dataset=train_dataset,
                    eval_dataset=eval_dataset,
                    epochs=stage3_epochs,
                    stage_name="Stage 3 Relations",
                    max_row_count=max_row_count,
                    save_intermediate=save_intermediate,
                    learning_rate=1e-5  # Lower LR for fine-tuning
                )

        except Exception as e:
            print(f"\n❌ Error during staged training: {e}")
            print("Restoring original loss weights...")
            self.update_loss_weights(**original_weights)
            raise e

        # Final model save with versioned naming
        final_model_path = generate_model_save_path("staged_training_final")
        os.makedirs(final_model_path, exist_ok=True)

        self.save_pretrained(final_model_path)
        tokenizer.save_pretrained(final_model_path)

        print(f"\n{'='*80}")
        print(f"STAGED TRAINING COMPLETED SUCCESSFULLY!")
        print(f"{'='*80}")
        print(f"✅ Final model saved to: {final_model_path}")
        print(f"🕒 Save time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"\nTraining summary:")
        for stage, result in stage_results.items():
            print(f"  - {stage}: Completed successfully")

        # Automatically generate training visualization
        try:
            print(f"\n📊 Generating training visualization...")
            from loss_visualization import visualize_training_loss

            viz_results = visualize_training_loss(
                logs_dir="./logs",
                output_dir="./visualizations",
                run_name="staged_training",
                show_plots=False,  # Don't show during training
                save_plots=True
            )

            if viz_results:
                print(f"✅ Training visualization saved to ./visualizations/")
                if viz_results['issues']:
                    print(f"⚠️ Detected {len(viz_results['issues'])} training issues")
                if viz_results['recommendations']:
                    print(f"💡 Generated {len(viz_results['recommendations'])} recommendations")

        except Exception as e:
            print(f"⚠️ Could not generate visualization: {e}")

        return stage_results
    
    @staticmethod
    def load_labels_fromfile(filepath):
        """
        加载标签文件并创建标签映射

        修复：统一使用0-based索引策略
        - 标签ID从0开始，与模型输出的logits索引一致
        - 简化索引转换逻辑，避免1-based和0-based之间的混乱
        """
        labelmap = {}
        json_file = open(filepath, "r", encoding="utf-8")
        json_data = json.load(json_file)
        for idx, item in enumerate(json_data):
            label = item["label"]
            # 修复：统一使用0-based索引，与模型logits输出一致
            labelmap[label] = idx
        json_file.close()

        idary = list(labelmap.keys())

        print(f"📋 Loaded {len(labelmap)} labels from {filepath}")
        print(f"   Sample mappings: {dict(list(labelmap.items())[:5])}")

        return labelmap, idary

    @staticmethod
    def validate_data_consistency(tokenized_dataset, grammar_id_map, subdomain_id_map, sample_size=3):
        """
        验证预处理后的数据格式一致性

        Args:
            tokenized_dataset: 预处理后的数据集
            grammar_id_map: 语法标签映射
            subdomain_id_map: 子域标签映射
            sample_size: 检查的样本数量
        """
        print(f"\n🔍 数据一致性验证 (检查前{sample_size}个样本):")

        if len(tokenized_dataset) == 0:
            print("❌ 数据集为空")
            return False

        sample = tokenized_dataset[0]
        print(f"✅ 数据集大小: {len(tokenized_dataset)}")
        print(f"✅ 样本键: {list(sample.keys())}")

        # 检查标签映射
        print(f"\n📋 标签映射信息:")
        print(f"  - 语法标签数量: {len(grammar_id_map)}")
        print(f"  - 子域标签数量: {len(subdomain_id_map)}")
        print(f"  - 语法标签范围: [0, {len(grammar_id_map)-1}]")
        print(f"  - 子域标签范围: [0, {len(subdomain_id_map)-1}]")

        # 检查样本数据
        for i in range(min(sample_size, len(tokenized_dataset))):
            sample = tokenized_dataset[i]
            print(f"\n📝 样本 {i+1}:")

            # 检查grammar_labels
            grammar_labels = sample['grammar_labels']
            print(f"  - grammar_labels类型: {type(grammar_labels)}")
            if hasattr(grammar_labels, 'shape'):
                print(f"  - grammar_labels形状: {grammar_labels.shape}")
                print(f"  - grammar_labels样例: {grammar_labels.tolist()}")

                # 检查标签值范围
                active_labels = [i for i, val in enumerate(grammar_labels) if val == 1]
                print(f"  - 激活的语法标签索引: {active_labels}")
                if any(idx >= len(grammar_id_map) for idx in active_labels):
                    print(f"❌ 警告: 发现超出范围的语法标签索引")
            else:
                print(f"  - grammar_labels长度: {len(grammar_labels) if hasattr(grammar_labels, '__len__') else 'N/A'}")

            # 检查subdomain_labels
            subdomain_label = sample['subdomain_labels']
            print(f"  - subdomain_labels类型: {type(subdomain_label)}")
            if hasattr(subdomain_label, 'item'):
                subdomain_value = subdomain_label.item()
                print(f"  - subdomain_labels值: {subdomain_value}")
                if subdomain_value >= len(subdomain_id_map):
                    print(f"❌ 警告: 发现超出范围的子域标签 {subdomain_value} (最大应为 {len(subdomain_id_map)-1})")
            else:
                print(f"  - subdomain_labels值: {subdomain_label}")

        print(f"\n✅ 数据一致性验证完成")
        return True
        
    @staticmethod
    def cvt_labsjson_to_trainjson(labsjson):

        train_json = []
        for item in labsjson:

            # get sentence
            sentence = item["data"]["text"]
            if not sentence:
                print("Warning: Sentence is empty, skipping this item.")
                continue

            # get grammar labels, from_name is "style" and type is "choices"
            grammar_labels = []
            for annotation in item["annotations"]:
                if not "result" in annotation:
                    continue
                for result in annotation["result"]:
                    if not "type" in result or result["type"] != "choices":
                        continue
                    # label studio intent label name, can change before annotation
                    if result["from_name"] != "style":
                        continue
                    if not "value" in result or not "choices" in result["value"] or len(result["value"]["choices"]) == 0:
                        print(f"Warning: {sentence} Grammar label has no choices.")
                        continue
                    # build train label
                    grammar_labels = result["value"]["choices"]
                    break
            if len(grammar_labels) == 0:
                print(f"Warning: {sentence} Grammar label is empty, using default 'unknown'.")
                grammar_labels = ["unknown"]

            # get subdomain label, from_name is "subdomain" and type is "choices"
            subdomain_label = None
            for annotation in item["annotations"]:
                if not "result" in annotation:
                    continue
                for result in annotation["result"]:
                    if not "type" in result or result["type"] != "choices":
                        continue
                    # label studio intent label name, can change before annotation
                    if result["from_name"] != "subdomain":
                        continue
                    if not "value" in result or not "choices" in result["value"] or len(result["value"]["choices"]) == 0:
                        print(f"Warning: {sentence} Subdomain label has no choices.")
                        continue
                    # build train label
                    subdomain_label = result["value"]["choices"][0]
                    break
            if subdomain_label is None:
                print(f"Warning: {sentence} Subdomain label is empty, using default 'unknown'.")
                subdomain_label = "unknown"

            # get entity spans, from_name is "label" and type is "labels"
            entity_spans = []
            for annotation in item["annotations"]:
                if not "result" in annotation:
                    continue
                for result in annotation["result"]:
                    if not "type" in result or result["type"] != "labels":
                        continue
                    # label studio entity label name, can change before annotation
                    if result["from_name"] != "label":
                        continue
                    if not "value" in result or not "start" in result["value"] or not "end" in result["value"]:
                        print(f"Warning: {sentence} Entity span has no start or end.")
                        continue
                    if not "labels" in result["value"] or len(result["value"]["labels"]) == 0:
                        print(f"Warning: {sentence} Entity span has no label.")
                        continue
                    # build train label
                    span = {
                        "id": result["id"],
                        "start": result["value"]["start"],
                        "end": result["value"]["end"],
                        "type": result["value"]["labels"][0]
                    }
                    entity_spans.append(span)
            # get entity relations, from_id is not empty and type is "relation"
            entity_relations = []
            for annotation in item["annotations"]:
                if not "result" in annotation:
                    continue
                for result in annotation["result"]:
                    if not "type" in result or result["type"] != "relation":
                        continue
                    # search entity spans match id find the start and end index
                    from_entity = next((e for e in entity_spans if e["id"] == result["from_id"]), None)
                    to_entity = next((e for e in entity_spans if e["id"] == result["to_id"]), None)
                    if from_entity is None or to_entity is None:
                        print(f"Warning: {sentence} Entity relation has invalid entity references.")
                        continue
                    if not "labels" in result or len(result["labels"]) == 0:
                        print(f"Warning: {sentence} Entity relation has no label.")
                        continue
                    # build train label
                    relation = {
                        "start_i": from_entity["start"],
                        "end_i": from_entity["end"],
                        "start_j": to_entity["start"],
                        "end_j": to_entity["end"],
                        "type": result["labels"][0]
                    }
                    entity_relations.append(relation)
            
            # remove entity spans id column after used in entity_relations
            entity_spans = [{"start": e["start"], "end": e["end"], "type": e["type"]} for e in entity_spans]

            # append to train json
            train_json.append({
                "sentence": sentence,
                "grammar_labels": grammar_labels,
                "subdomain_label": subdomain_label,
                "entity_spans": entity_spans,
                "relation_pairs": entity_relations
            })

        return train_json

def main(my_dataset=None, my_train_dataset=None, my_eval_dataset=None, my_model=None, my_tokenizer=None):

    return_code = -1

    options = [
        "1. Load training dataset",
        "2. Concat additional datasets",
        "3. Load pre-trained model",
        "4. Train model with loaded datasets",
        "5. Run inference with test data",
        "6. Convert LabelStudio export to training format",
        "7. Save model to my_models directory",
        "8. Train model with staged approach",
        "9. Visualize training loss",
        "10. Hyperparameter optimization (超参数优化)",
        "0. Exit"
    ]

    # if my_model is not None then display the model path behind the option
    if my_model is not None:
        options[2] = f"3. Load pre-trained model (model: {my_model.name_or_path})"
    
    # if my_train_dataset is not None and my_eval_dataset is not None, display the dataset size behind the option
    if my_train_dataset is not None and my_eval_dataset is not None:
        options[0] = f"1. Load training dataset (train size: {len(my_train_dataset)}, eval size: {len(my_eval_dataset)})"
        options[1] = f"2. Concat additional datasets (train size: {len(my_train_dataset)}, eval size: {len(my_eval_dataset)})"

    terminal_menu = TerminalMenu(options, title="=== Conversation Model Training Interface ===")
    choice_index = terminal_menu.show()
    if choice_index is not None:
        # 正确提取选项编号：从选项字符串中提取数字部分
        choice = options[choice_index].split('.')[0].strip()
    else:
        choice = "0"

    if choice == "0":
        return_code = 0
        print("Exiting program.")
        return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
    
    # load or concat additional datasets
    elif choice == "1" or choice == "2":

        # clear previous dataset if is loading new dataset
        if choice == "1":
            my_train_dataset = None
            my_eval_dataset = None

        # List all files in the datasets directory
        dataset_files = [f for f in os.listdir("./datasets/") if f.endswith(".json")]
    
        # use terminal menu to select a dataset
        if not dataset_files:
            print("No JSON dataset files found in ./datasets/ directory.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        
        dataset_files.sort()
        dataset_menu = [f"./datasets/{f}" for f in dataset_files]
        dataset_menu = [f"{i+1}. {f}" for i, f in enumerate(dataset_menu)]
        dataset_menu = [f"0. Clear dataset loaded"] + dataset_menu

        terminal_menu = TerminalMenu(dataset_menu, title="=== Available datasets ===")
        choice_index = terminal_menu.show()
        if choice_index is not None:
            # Extract the number before the period in the menu item
            choice = int(dataset_menu[choice_index].split('.')[0])
        else:
            choice = 0
        
        try:

            if 0 <= choice < len(dataset_files):
            
                dataset_path = dataset_files[choice-1]
                print(f"Loading {dataset_path}...")
            
                additional_dataset = load_dataset("json", data_files=f"./datasets/{dataset_path}")
                additional_split = additional_dataset["train"].train_test_split(test_size=0.1)
            
                if my_train_dataset is not None and my_eval_dataset is not None:
                    my_train_dataset = concatenate_datasets([my_train_dataset, additional_split["train"]])
                    my_eval_dataset = concatenate_datasets([my_eval_dataset, additional_split["test"]])
                    my_train_dataset = my_train_dataset.shuffle(seed=42)
                    my_eval_dataset = my_eval_dataset.shuffle(seed=42)
                    print(f"Datasets combined. New sizes - Training: {len(my_train_dataset)}, Evaluation: {len(my_eval_dataset)}")
                else:
                    my_train_dataset = additional_split["train"]
                    my_eval_dataset = additional_split["test"]
                    print(f"Dataset loaded. Training samples: {len(my_train_dataset)}, Evaluation samples: {len(my_eval_dataset)}")

                print("dataset columns: ", my_train_dataset.column_names)
                
            elif choice != 0:
                print("Invalid selection.")
            elif choice == 0:
                print("Clearing loaded datasets.")
                my_train_dataset = None
                my_eval_dataset = None
        except ValueError:
            print("Please enter a valid number.") 
        except Exception as e:
            print(f"Error loading dataset: {e}")
            print("Please try again.")

    # Load pre-trained model
    elif choice == "3":

        # List available model directories
        available_models = []
        # Find the deepest level directories in dl_models
        if os.path.exists("./dl_models/"):
            for root, dirs, files in os.walk("./dl_models/"):
                # If this is a leaf directory (no subdirectories)
                if not dirs:
                    available_models.append(root)            

        if os.path.exists("./my_models/"):
            my_models = [f"./my_models/{d}" for d in os.listdir("./my_models/") if os.path.isdir(f"./my_models/{d}")]
            available_models.extend(my_models)
        
        if not available_models:
            print("No available models found in ./dl_models/ or ./my_models/ directories.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        
        available_models.sort()
        models_menu = [f"{i+1}. {f}" for i, f in enumerate(available_models)]
        models_menu = [f"0. Cancel"] + models_menu

        terminal_menu = TerminalMenu(models_menu, title="=== Available models ===")
        choice_index = terminal_menu.show()
        if choice_index is not None:
            choice = int(models_menu[choice_index].split('.')[0])
        else:
            choice = 0
        
        if 0 <= choice-1 < len(available_models):
            model_path = available_models[choice-1]
        else:
            print("Invalid selection.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        
        print(f"Loading model from {model_path}...")
        try:
            my_model = RoBERTaForConversationClassification.from_pretrained(model_path)
            my_tokenizer = BertTokenizerFast.from_pretrained(model_path)
            print(f"Model loaded successfully from {model_path}")
        except Exception as e:
            print(f"Error loading model: {e}")

    # Train model with loaded datasets
    elif choice == "4":

        if my_train_dataset is None or my_eval_dataset is None:
            print("No datasets loaded. Please load datasets first (options 1-2).")
        elif 'my_model' in locals() and 'my_tokenizer' in locals():

            # Create menu for epoch selection
            epoch_options = ["1 epoch", "3 epochs", "5 epochs", "10 epochs", "Custom..."]
            epoch_menu = TerminalMenu(epoch_options, title="Select number of training epochs:")
            epoch_idx = epoch_menu.show()
            
            if epoch_idx == 4:  # Custom option
                epochs_input = input("Enter custom number of epochs: ").strip()
                epochs_input = epochs_input.replace("\r", "").replace("\n", "")
                epochs = int(epochs_input or "3")
            else:
                # Extract number from selected option
                epochs = int(epoch_options[epoch_idx].split()[0]) 

            # Create menu for save steps selection
            save_steps_options = ["100 steps", "500 steps", "1000 steps", "5000 steps", "Custom..."]
            save_steps_menu = TerminalMenu(save_steps_options, title="Select save frequency:")
            save_steps_idx = save_steps_menu.show()
            
            if save_steps_idx == 4:  # Custom option
                save_steps_input = input("Enter custom save steps: ").strip()
                save_steps_input = save_steps_input.replace("\r", "").replace("\n", "")
                save_steps = int(save_steps_input or "100")
            else:
                # Extract number from selected option
                save_steps = int(save_steps_options[save_steps_idx].split()[0]) 

            # 询问是否自动保存模型
            auto_save_options = ["Yes, auto-save after training", "No, I'll save manually later"]
            auto_save_menu = TerminalMenu(auto_save_options, title="Auto-save model after training?")
            auto_save_idx = auto_save_menu.show()
            auto_save = (auto_save_idx == 0)

            # 如果选择自动保存，询问模型名称
            custom_name = None
            if auto_save:
                custom_name = input("\n请输入模型名称 (留空使用默认名称 'trained_model'): ").strip()
                if not custom_name:
                    custom_name = "trained_model"
                print(f"模型将自动保存为: {custom_name}_YYYYMMDD_HHMMSS")

            print(f"\nTraining model with {len(my_train_dataset)} samples for {epochs} epochs...")

            try:
                my_model.custom_train(
                    tokenizer=my_tokenizer,
                    train_dataset=my_train_dataset,
                    eval_dataset=my_eval_dataset,
                    max_row_count=None,
                    custom_epoch=epochs,
                    save_steps=save_steps,
                    auto_save=auto_save,
                    custom_name=custom_name
                )
            except Exception as e:
                print(f"Error during training: {e}")
    
    # Run inference with test data
    elif choice == "5":
        if 'my_model' not in locals() or 'my_tokenizer' not in locals():
            print("No model loaded. Please load a model first (option 3).")
        else:
            # load inference sentence from file or custom input
            if not os.path.exists("./datasets/my_roberta_v2_testdata.txt"):
                print("No test data file found at ./datasets/my_roberta_v2_testdata.txt. Please create this file with test sentences.")
                return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
            # read default sentences from file
            with open("./datasets/my_roberta_v2_testdata.txt", "r", encoding="utf-8") as f:
                # read lines, delete empty lines and strip whitespace
                default_sentences = f.readlines()
                default_sentences = [s.strip() for s in default_sentences if s.strip()]
                if not default_sentences:
                    print("No valid sentences found in ./datasets/my_roberta_v2_testdata.txt. Please add some sentences.")
                    return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
            
            # display sentence select menu or custom input
            option_menu = [f"{i+1}. {f}" for i, f in enumerate(default_sentences)]
            option_menu = [f"0. Custom input, Empty means quit"] + option_menu
            terminal_menu = TerminalMenu(option_menu, title="=== Available sentences ===")
            choice_index = terminal_menu.show()
            if choice_index is not None:
                choice = int(option_menu[choice_index].split('.')[0])
            else:
                choice = 0
            if choice == 0:
                test_sentence = input("Enter test sentence (default: deflist): ")
                if not test_sentence:
                    print("No sentence entered. Exiting.")
                    return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
                else:
                    test_sentence = [test_sentence]
            else:
                test_sentence = [default_sentences[choice-1]]
                print(f"Selected sentence: {test_sentence[0]}")

            try:
                print(f"Running inference on {test_sentence}...")
                inference_output = my_model.inference(test_sentence, my_tokenizer)
                for i, entry in enumerate(inference_output):
                    print(f"\n--- Result {i+1} ---")
                    print(f"Sentence: {entry['sentence']}")
                    print(f"Grammar: {entry['grammar']}")
                    print(f"Subdomain: {entry['subdomain']}")
                    # print each entity with new line
                    print("Entities:")
                    for entity in entry['entities']:
                        print(f"  - [{entity['start']:02d}:{entity['end']:02d}] ({entity['type']}) {entity['value']}")
                    # print each relation with new line
                    print("Relations:")
                    for relation in entry['relations']:
                        print(f"  - [{relation['entity1']['start']:02d}:{relation['entity1']['end']:02d}] ({relation['entity1']['type']}) ({relation['entity1']['value']})"
                              f" ={relation['relation_type']}= "
                              f"[{relation['entity2']['start']:02d}:{relation['entity2']['end']:02d}] ({relation['entity2']['type']}) ({relation['entity2']['value']})")
            except Exception as e:
                print(f"Error during inference: {e}")

    elif choice == "6":
        # display all the json files in datasets directory
        labsjson_files = [f for f in os.listdir("./datasets/") if f.endswith(".json")]
        # display menu to select a file
        if not labsjson_files:
            print("No LabelStudio export files found in ./datasets/ directory.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        labsjson_files.sort()
        labsjson_menu = [f"{i+1}. {f}" for i, f in enumerate(labsjson_files)]
        labsjson_menu = [f"0. Cancel"] + labsjson_menu
        terminal_menu = TerminalMenu(labsjson_menu, title="=== Available LabelStudio export files ===")
        choice_index = terminal_menu.show()
        if choice_index is not None:
            choice = int(labsjson_menu[choice_index].split('.')[0])
        else:
            choice = 0
        if choice == 0:
            print("Cancelled conversion.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        input_file = "./datasets/" + labsjson_files[choice-1]

        # display menu to select output file    
        option = [
            "./datasets/my_roberta_v2_traindata.json",
            "./datasets/my_roberta_v2_traindata_error.json",
            # "./datasets/my_roberta_v2_traindata_test_0630_1645.json"
            # "./datasets/my_roberta_v2_traindata_test_0702_1101.json"

        ]
        option_menu = [f"{i+1}. {f}" for i, f in enumerate(option)]
        option_menu = [f"0. Custom output file"] + option_menu
        terminal_menu = TerminalMenu(option_menu, title="=== Select output file ===")
        choice_index = terminal_menu.show()
        if choice_index == 0:
            output_file = input("Enter output file path: ").strip()
            if not output_file or not output_file.endswith(".json"): # empty output file means exit or not end with .json
                print("Invalid output file path. Exiting conversion.")
                return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer
        elif choice_index == 1 or choice_index == 2:
            output_file = option[choice_index-1]
        else:
            print("Invalid selection.")
            return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer

        try:
            with open(input_file, "r", encoding="utf-8") as f:
                labsjson = json.load(f)
            trainjson = RoBERTaForConversationClassification.cvt_labsjson_to_trainjson(labsjson)
            # if output file does not exist, create it
            if not os.path.exists(os.path.dirname(output_file)):
                os.makedirs(os.path.dirname(output_file))
            # write trainjson to output file
            print(f"Writing converted data to {output_file}...")
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(trainjson, f, ensure_ascii=False, indent=2)
            print(f"Converted {len(trainjson)} samples and saved to {output_file}")
        except Exception as e:
            print(f"Error in conversion: {e}")

    elif choice == "7":
        # Save model with versioned naming
        if my_model is None or my_tokenizer is None:
            print("No model loaded. Please load a model first (option 3).")
        else:
            print("\n" + "="*50)
            print("模型保存配置")
            print("="*50)

            # 获取自定义模型名称
            custom_name = input("请输入模型名称 (留空使用默认名称 'roberta_model'): ").strip()
            if not custom_name:
                custom_name = "roberta_model"

            # 生成带时间戳的保存路径
            save_path = generate_model_save_path(custom_name)

            # 确保保存目录存在
            os.makedirs(save_path, exist_ok=True)

            # 保存模型和tokenizer
            try:
                print(f"\n正在保存模型到: {save_path}")
                my_model.save_pretrained(save_path)
                my_tokenizer.save_pretrained(save_path)

                print(f"\n✅ 模型保存成功!")
                print(f"📁 保存路径: {save_path}")
                print(f"📝 模型名称: {custom_name}")
                print(f"🕒 保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                # 显示保存的文件
                saved_files = os.listdir(save_path)
                print(f"📄 保存的文件: {', '.join(saved_files)}")

            except Exception as e:
                print(f"❌ 保存模型时出错: {e}")

    elif choice == "8":
        # Train model with staged approach
        if my_train_dataset is None or my_eval_dataset is None:
            print("No datasets loaded. Please load datasets first (options 1-2).")
        elif my_model is None or my_tokenizer is None:
            print("No model loaded. Please load a model first (option 3).")
        else:
            try:
                # Create menu for total epochs selection
                epoch_options = ["3 epochs", "5 epochs", "7 epochs", "10 epochs", "Custom..."]
                epoch_menu = TerminalMenu(epoch_options, title="Select total epochs for staged training:")
                epoch_idx = epoch_menu.show()

                if epoch_idx == 4:  # Custom option
                    epochs_input = input("Enter custom number of total epochs: ").strip()
                    total_epochs = int(epochs_input) if epochs_input.isdigit() else 5
                else:
                    # Extract number from selected option
                    total_epochs = int(epoch_options[epoch_idx].split()[0])

                # Create menu for save intermediate checkpoints
                save_options = ["Save intermediate checkpoints", "No intermediate saves"]
                save_menu = TerminalMenu(save_options, title="Save intermediate checkpoints?")
                save_idx = save_menu.show()
                save_intermediate = (save_idx == 0)

                # Create menu for resume stage
                resume_options = ["Start from Stage 1", "Resume from Stage 2", "Resume from Stage 3"]
                resume_menu = TerminalMenu(resume_options, title="Select starting stage:")
                resume_idx = resume_menu.show()
                resume_from_stage = resume_idx + 1

                print(f"\nStarting staged training with:")
                print(f"  - Total epochs: {total_epochs}")
                print(f"  - Save intermediate: {save_intermediate}")
                print(f"  - Resume from stage: {resume_from_stage}")
                print(f"  - Training samples: {len(my_train_dataset)}")
                print(f"  - Evaluation samples: {len(my_eval_dataset)}")

                # Confirm before starting
                confirm = input("\nProceed with staged training? (y/N): ").strip().lower()
                if confirm == 'y':
                    stage_results = my_model.staged_training(
                        tokenizer=my_tokenizer,
                        train_dataset=my_train_dataset,
                        eval_dataset=my_eval_dataset,
                        custom_epoch=total_epochs,
                        save_intermediate=save_intermediate,
                        resume_from_stage=resume_from_stage
                    )
                    print("\n✅ Staged training completed successfully!")
                    print("Check ./my_models/staged_training_final for the final model.")
                else:
                    print("Staged training cancelled.")

            except Exception as e:
                print(f"Error during staged training: {e}")
                import traceback
                traceback.print_exc()

    elif choice == "9":
        # Visualize training loss
        try:
            from loss_visualization import visualize_training_loss

            # Create menu for visualization options
            viz_options = [
                "Visualize current training logs",
                "Visualize specific log directory",
                "Compare multiple training runs",
                "Generate report only (no plots)"
            ]
            viz_menu = TerminalMenu(viz_options, title="Select visualization option:")
            viz_idx = viz_menu.show()

            if viz_idx == 0:
                # Visualize current logs
                print("\n📊 Analyzing current training logs...")
                results = visualize_training_loss(
                    logs_dir="./logs",
                    output_dir="./visualizations",
                    run_name="current",
                    show_plots=True,
                    save_plots=True
                )

                if results:
                    print(f"\n✅ Analysis complete!")
                    print(f"   Generated {len(results['figures'])} plots")
                    print(f"   Found {len(results['issues'])} issues")
                    print(f"   Generated {len(results['recommendations'])} recommendations")

                    if results['issues']:
                        print("\n⚠️ Issues detected:")
                        for issue in results['issues']:
                            print(f"   - {issue}")

                    if results['recommendations']:
                        print("\n💡 Recommendations:")
                        for rec in results['recommendations']:
                            print(f"   - {rec}")
                else:
                    print("❌ No training logs found to analyze")

            elif viz_idx == 1:
                # Visualize specific directory
                logs_dir = input("Enter path to logs directory: ").strip()
                if not logs_dir:
                    logs_dir = "./logs"

                if os.path.exists(logs_dir):
                    run_name = input("Enter run name (optional): ").strip()
                    if not run_name:
                        run_name = "custom"

                    results = visualize_training_loss(
                        logs_dir=logs_dir,
                        output_dir="./visualizations",
                        run_name=run_name,
                        show_plots=True,
                        save_plots=True
                    )

                    if results:
                        print(f"\n✅ Analysis complete for {logs_dir}")
                    else:
                        print(f"❌ No valid logs found in {logs_dir}")
                else:
                    print(f"❌ Directory {logs_dir} does not exist")

            elif viz_idx == 2:
                # Compare multiple runs
                print("\n🔄 Setting up training run comparison...")
                print("Enter training run configurations (press Enter with empty name to finish):")

                run_configs = []
                while True:
                    name = input(f"Run {len(run_configs)+1} name: ").strip()
                    if not name:
                        break

                    logs_dir = input(f"Run {len(run_configs)+1} logs directory: ").strip()
                    if not logs_dir:
                        logs_dir = "./logs"

                    if os.path.exists(logs_dir):
                        run_configs.append({'name': name, 'logs_dir': logs_dir})
                        print(f"   Added: {name} -> {logs_dir}")
                    else:
                        print(f"   Skipped: {logs_dir} does not exist")

                if len(run_configs) >= 2:
                    from loss_visualization import compare_training_runs
                    results = compare_training_runs(run_configs, "./visualizations")
                    print(f"\n✅ Comparison complete! Check ./visualizations/")
                else:
                    print("❌ Need at least 2 valid training runs for comparison")

            elif viz_idx == 3:
                # Report only
                results = visualize_training_loss(
                    logs_dir="./logs",
                    output_dir="./visualizations",
                    run_name="report_only",
                    show_plots=False,
                    save_plots=False
                )

                if results and results['report']:
                    print("\n📋 Training Analysis Report:")
                    print("=" * 50)
                    print(results['report'])
                else:
                    print("❌ No training data found for report generation")

        except ImportError:
            print("❌ Loss visualization module not available.")
            print("   Make sure loss_visualization.py is in the current directory.")
        except Exception as e:
            print(f"❌ Error during visualization: {e}")
            import traceback
            traceback.print_exc()

    # Hyperparameter optimization
    elif choice == "10":
        if my_train_dataset is None or my_eval_dataset is None:
            print("No datasets loaded. Please load datasets first (options 1-2).")
        elif my_model is None or my_tokenizer is None:
            print("No model loaded. Please load a model first (option 3).")
        else:
            print("\n" + "="*60)
            print("🤖 基础训练参数优化 (Basic Training Parameter Optimization)")
            print("="*60)
            print("专注于优化custom_train方法的基础参数：learning_rate, batch_size, epochs等")
            print()

            optimization_options = [
                "1. 网格搜索优化 (Grid Search) - 全面搜索最佳参数组合",
                "2. 随机搜索优化 (Random Search) - 快速搜索较好参数",
                "3. 查看优化结果 (View Results) - 分析历史优化结果",
                "4. 应用最佳参数训练 (Train with Best Params) - 使用最佳参数训练模型",
                "0. 返回主菜单 (Back to Main Menu)"
            ]

            for opt in optimization_options:
                print(opt)

            try:
                opt_choice = input("\n请选择操作 (0-4): ").strip()

                if opt_choice == "0":
                    print("返回主菜单...")
                    # 直接返回，不进入无限循环

                elif opt_choice == "1" or opt_choice == "2":
                    search_method = 'grid' if opt_choice == "1" else 'random'
                    max_trials = 12 if opt_choice == "1" else 15

                    print(f"\n正在启动{search_method}搜索优化...")
                    print("⚠️ 注意: 超参数优化可能需要30-60分钟，请耐心等待")
                    print(f"📊 将测试 {max_trials} 组参数组合")

                    # 确认是否继续
                    confirm = input("是否继续? (y/N): ").strip().lower()
                    if confirm != 'y':
                        print("已取消优化")
                    else:
                        try:
                            # 导入简化的优化模块
                            from simple_hyperparameter_optimizer import SimpleHyperparameterOptimizer, SimpleOptimizationConfig

                            # 配置优化器
                            config = SimpleOptimizationConfig(
                                search_method=search_method,
                                max_trials=max_trials,
                                target_metric='combined_f1',
                                results_dir='./simple_optimization_results',
                                save_best_model=True
                            )

                            # 创建优化器
                            optimizer = SimpleHyperparameterOptimizer(config)

                            # 执行优化
                            print(f"🚀 开始执行{search_method}搜索优化...")
                            results = optimizer.optimize(my_model, my_tokenizer, my_train_dataset, my_eval_dataset)

                            print(f"\n✅ 优化完成!")

                            # 检查是否有有效结果
                            best_score = results.get('best_score', float('-inf'))
                            best_parameters = results.get('best_parameters')

                            if best_score > float('-inf') and best_parameters:
                                print(f"📊 最佳分数: {best_score:.4f}")
                                print(f"🎯 最佳参数:")
                                for key, value in best_parameters.items():
                                    if isinstance(value, float):
                                        print(f"   - {key}: {value:.6f}")
                                    else:
                                        print(f"   - {key}: {value}")
                                print(f"📁 结果保存在: {config.results_dir}")

                                # 询问是否立即使用最佳参数训练
                                try:
                                    use_best = input("\n是否立即使用最佳参数重新训练模型? (y/N): ").strip().lower()
                                    if use_best == 'y':
                                        best_params = optimizer.get_best_parameters_for_custom_train()
                                        if best_params:
                                            print("🚀 使用最佳参数开始训练...")
                                            my_model.custom_train(
                                                tokenizer=my_tokenizer,
                                                train_dataset=my_train_dataset,
                                                eval_dataset=my_eval_dataset,
                                                max_row_count=None,
                                                custom_epoch=best_params.get('custom_epoch', 3),
                                                save_steps=best_params.get('save_steps', 500),
                                                auto_save=True,
                                                custom_name=f"optimized_model_{search_method}",
                                                learning_rate=best_params.get('learning_rate', 2e-5),
                                                per_device_train_batch_size=best_params.get('per_device_train_batch_size', 1),
                                                gradient_accumulation_steps=best_params.get('gradient_accumulation_steps', 4),
                                                warmup_ratio=best_params.get('warmup_ratio', 0.01),
                                                weight_decay=best_params.get('weight_decay', 0.0),
                                                lr_scheduler_type=best_params.get('lr_scheduler_type', 'cosine'),
                                                # 多任务权重参数
                                                weight_grammar=best_params.get('weight_grammar', 1.0),
                                                weight_subdomain=best_params.get('weight_subdomain', 1.0),
                                                weight_position=best_params.get('weight_position', 1.0),
                                                weight_entity_type=best_params.get('weight_entity_type', 1.0),
                                                weight_relation=best_params.get('weight_relation', 1.0)
                                            )
                                            print("✅ 使用最佳参数的训练完成!")
                                        else:
                                            print("❌ 无法获取最佳参数")
                                except KeyboardInterrupt:
                                    print("\n⚠️ 用户取消操作")
                                except Exception as train_error:
                                    print(f"❌ 训练过程出错: {train_error}")
                            else:
                                print(f"❌ 优化失败: 所有试验都失败了")
                                print(f"📊 最佳分数: {best_score}")
                                print(f"📁 详细结果保存在: {config.results_dir}")
                                print("💡 建议检查数据格式和模型配置")

                        except ImportError as e:
                            print(f"❌ 导入优化模块失败: {e}")
                            print("请确保 simple_hyperparameter_optimizer.py 文件在当前目录下")
                        except Exception as e:
                            print(f"❌ 优化过程出错: {e}")
                            import traceback
                            traceback.print_exc()

                elif opt_choice == "3":  # View Results
                    try:
                        # import json
                        import glob

                        print("\n📊 分析优化结果...")

                        # 查找结果文件
                        result_files = glob.glob('./simple_optimization_results/final_optimization_results.json')
                        result_files.extend(glob.glob('./simple_optimization_results/optimization_results_*.json'))

                        if not result_files:
                            print("❌ 未找到优化结果文件")
                            print("请先运行优化后再查看结果")
                        else:
                            print(f"📁 找到 {len(result_files)} 个结果文件:")

                            best_overall = {'score': float('-inf'), 'params': None}

                            for i, file_path in enumerate(result_files, 1):
                                try:
                                    with open(file_path, 'r', encoding='utf-8') as f:
                                        data = json.load(f)

                                    score = data.get('best_score', float('-inf'))
                                    params = data.get('best_params') or data.get('best_parameters')

                                    print(f"  {i}. {os.path.basename(file_path)}: 最佳分数 {score:.4f}")

                                    if score > best_overall['score']:
                                        best_overall.update({
                                            'score': score,
                                            'params': params,
                                            'file': file_path
                                        })

                                except Exception as e:
                                    print(f"  {i}. 读取失败: {os.path.basename(file_path)} - {e}")

                            if best_overall['params']:
                                print(f"\n🏆 历史最佳结果 (分数: {best_overall['score']:.4f}):")
                                print(f"📋 最佳参数:")
                                for key, value in best_overall['params'].items():
                                    if isinstance(value, float):
                                        print(f"  - {key}: {value:.6f}")
                                    else:
                                        print(f"  - {key}: {value}")

                    except Exception as e:
                        print(f"❌ 分析结果时出错: {e}")

                elif opt_choice == "4":  # Train with Best Params
                    try:
                        # import json
                        import glob

                        # 查找最新的优化结果
                        result_files = glob.glob('./simple_optimization_results/final_optimization_results.json')

                        if not result_files:
                            print("❌ 未找到优化结果文件")
                            print("请先运行优化 (选项1或2) 后再使用此功能")
                        else:
                            # 读取最佳参数
                            with open(result_files[0], 'r', encoding='utf-8') as f:
                                data = json.load(f)

                            best_params = data.get('best_parameters')
                            best_score = data.get('best_score', 0)

                            if not best_params:
                                print("❌ 未找到最佳参数")
                            else:
                                print(f"📋 将使用以下最佳参数训练 (历史最佳分数: {best_score:.4f}):")
                                for key, value in best_params.items():
                                    if isinstance(value, float):
                                        print(f"  - {key}: {value:.6f}")
                                    else:
                                        print(f"  - {key}: {value}")

                                confirm = input("\n确认使用这些参数训练模型? (y/N): ").strip().lower()
                                if confirm == 'y':
                                    print("🚀 使用最佳参数开始训练...")

                                    # 转换参数格式并训练
                                    my_model.custom_train(
                                        tokenizer=my_tokenizer,
                                        train_dataset=my_train_dataset,
                                        eval_dataset=my_eval_dataset,
                                        max_row_count=None,
                                        custom_epoch=best_params.get('custom_epoch', 3),
                                        save_steps=best_params.get('save_steps', 500),
                                        auto_save=True,
                                        custom_name="optimized_best_model",
                                        learning_rate=best_params.get('learning_rate', 2e-5),
                                        per_device_train_batch_size=best_params.get('per_device_train_batch_size', 1),
                                        gradient_accumulation_steps=best_params.get('gradient_accumulation_steps', 4),
                                        warmup_ratio=best_params.get('warmup_ratio', 0.01),
                                        weight_decay=best_params.get('weight_decay', 0.0),
                                        lr_scheduler_type=best_params.get('lr_scheduler_type', 'cosine'),
                                        # 多任务权重参数
                                        weight_grammar=best_params.get('weight_grammar', 1.0),
                                        weight_subdomain=best_params.get('weight_subdomain', 1.0),
                                        weight_position=best_params.get('weight_position', 1.0),
                                        weight_entity_type=best_params.get('weight_entity_type', 1.0),
                                        weight_relation=best_params.get('weight_relation', 1.0)
                                    )
                                    print("✅ 使用最佳参数的训练完成!")
                                else:
                                    print("已取消训练")

                    except Exception as e:
                        print(f"❌ 处理最佳参数训练时出错: {e}")

                else:
                    print("❌ 无效选择")

            except Exception as e:
                print(f"❌ 处理优化选项时出错: {e}")

    else:
        print("Invalid option. Please try again.")
    
    return return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer

if __name__ == "__main__":

    import sys
    import termios
    import tty
    import atexit

    # set terminal to non-canonical mode
    fd = sys.stdin.fileno()
    original_settings = termios.tcgetattr(fd)
    def restore_terminal():
        termios.tcsetattr(fd, termios.TCSADRAIN, original_settings)
        print("\n[Terminal restored]")
    atexit.register(restore_terminal)
    tty.setcbreak(fd)
    # prevent termian abnormal exit

    CONVERSATION_GRAMMAR_ID_MAP, CONVERSATION_ID_GRAMMAR_ARY = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_grammar_labels.json")
    CONVERSATION_SUBDOMAIN_ID_MAP, CONVERSATION_ID_SUBDOMAIN_ARY = RoBERTaForConversationClassification.load_labels_fromfile("./datasets/my_roberta_subdomain_labels.json")

    my_dataset = None
    my_train_dataset = None
    my_eval_dataset = None
    my_model = None
    my_tokenizer = None

    while True:
        # Call the main function to start the menu
        return_code, my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer = main(my_dataset, my_train_dataset, my_eval_dataset, my_model, my_tokenizer)

        if return_code is None or return_code != 0:
            continue
        else:
            break

    print("Program terminated.")