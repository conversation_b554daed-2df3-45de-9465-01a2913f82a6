# 统一推理系统使用说明

## 概述

统一推理系统将语法/子域分类模型和实体/关系抽取模型结合，为单个输入句子提供全面的NLP分析结果。系统包含四个核心任务：

1. **语法分类** - 识别句子的语法类型（多标签）
2. **子域分类** - 确定句子所属的领域（单标签）
3. **实体识别** - 提取句子中的命名实体
4. **关系抽取** - 识别实体间的语义关系

## 文件结构

```
├── unified_inference.py          # 交互式推理脚本
├── unified_inference_api.py      # API接口模块
├── 推理方法分析文档.md            # 技术分析文档
├── 统一推理使用说明.md            # 本使用说明
├── my_roberta_grammar_subdomainv1.py    # 语法/子域模型
└── my_roberta_entity_relationV2.py      # 实体/关系模型
```

## 快速开始

### 1. 交互式使用

```bash
python unified_inference.py
```

系统会提供两种模式：
- **实际模型推理**：使用训练好的模型文件
- **示例测试**：使用模拟数据演示功能

### 2. API编程使用

```python
from unified_inference_api import unified_inference, batch_inference

# 单句推理
result = unified_inference("什么是机器学习的定义？")
print(result)

# 批量推理
sentences = ["句子1", "句子2", "句子3"]
results = batch_inference(sentences)
```

## 输出格式详解

### 统一输出结构

```python
{
    "sentence": "输入句子",
    "timestamp": "2024-01-01 12:00:00",
    "grammar": ["Query", "Definition"],        # 语法类型列表
    "subdomain": "AI/ML",                      # 子域名称
    "entities": [                              # 实体列表
        {
            "start": 2,                        # token级别开始位置
            "end": 4,                          # token级别结束位置
            "type": "Concept",                 # 实体类型
            "value": "机器学习"                # 实体文本值
        }
    ],
    "relations": [                             # 关系列表
        {
            "entity1": {                       # 第一个实体
                "start": 2, "end": 4,
                "type": "Concept", "value": "机器学习"
            },
            "entity2": {                       # 第二个实体
                "start": 5, "end": 6,
                "type": "Property", "value": "定义"
            },
            "relation_type": "Has_Property"    # 关系类型
        }
    ],
    "mode": "实际模型",                        # 运行模式
    "device": "cuda",                          # 使用设备
    "model_status": {                          # 模型状态
        "grammar_subdomain_model": "已加载",
        "entity_relation_model": "已加载"
    },
    "errors": []                               # 错误信息（可选）
}
```

### 任务特定输出

#### 语法分类 (Grammar Classification)
- **类型**: 多标签分类
- **输出**: 字符串列表
- **示例**: `["Query", "Definition", "Reason"]`
- **说明**: 一个句子可以有多个语法类型

#### 子域分类 (Subdomain Classification)
- **类型**: 单标签分类
- **输出**: 字符串
- **示例**: `"AI/ML"`
- **说明**: 一个句子只能属于一个子域

#### 实体识别 (Entity Recognition)
- **类型**: 序列标注
- **输出**: 字典列表
- **字段**:
  - `start/end`: token级别的位置索引
  - `type`: 实体类型（如Concept, Property等）
  - `value`: 实体的文本值

#### 关系抽取 (Relation Extraction)
- **类型**: 实体对分类
- **输出**: 字典列表
- **字段**:
  - `entity1/entity2`: 参与关系的两个实体
  - `relation_type`: 关系类型（如Has_Property等）

## 配置参数

### 推理参数

```python
result = unified_inference(
    sentence="测试句子",
    grammar_threshold=0.5,      # 语法分类阈值
    start_threshold=0.5,        # 实体开始位置阈值
    end_threshold=0.5           # 实体结束位置阈值
)
```

### 模型路径配置

```python
from unified_inference_api import get_api_instance

api = get_api_instance(
    grammar_model_path="./models/grammar_subdomain_model",
    entity_model_path="./models/entity_relation_model",
    use_mock=False  # 是否使用模拟模式
)
```

## 模型文件管理

### 自动查找路径

系统会自动在以下路径查找模型文件：

**语法/子域模型**:
- `./my_models/grammar_subdomain_model`
- `./dl_models/grammar_subdomain_model`
- `./models/grammar_subdomain_model`

**实体/关系模型**:
- `./my_models/entity_relation_model`
- `./dl_models/entity_relation_model`
- `./models/entity_relation_model`

### 手动指定路径

```python
# 方法1: 初始化时指定
api = UnifiedInferenceAPI(
    grammar_model_path="/path/to/grammar_model",
    entity_model_path="/path/to/entity_model"
)

# 方法2: 环境变量
import os
os.environ['GRAMMAR_MODEL_PATH'] = "/path/to/grammar_model"
os.environ['ENTITY_MODEL_PATH'] = "/path/to/entity_model"
```

## 错误处理

### 常见错误类型

1. **模型加载失败**
   - 原因：模型文件不存在或损坏
   - 解决：检查模型路径，重新训练模型

2. **推理失败**
   - 原因：输入格式错误或模型兼容性问题
   - 解决：检查输入格式，更新模型版本

3. **内存不足**
   - 原因：模型太大或批处理数据过多
   - 解决：减少批处理大小，使用CPU推理

### 错误处理示例

```python
try:
    result = unified_inference("测试句子")
    if "errors" in result:
        print("推理警告:", result["errors"])
except Exception as e:
    print(f"推理失败: {e}")
    # 切换到模拟模式
    result = unified_inference("测试句子", use_mock=True)
```

## 性能优化

### 批处理优化

```python
# 推荐：批量处理
sentences = ["句子1", "句子2", "句子3"]
results = batch_inference(sentences)

# 不推荐：逐个处理
results = []
for sentence in sentences:
    result = unified_inference(sentence)
    results.append(result)
```

### GPU加速

```python
import torch

# 检查GPU可用性
if torch.cuda.is_available():
    print(f"使用GPU: {torch.cuda.get_device_name()}")
else:
    print("使用CPU推理")
```

### 内存管理

```python
# 清理GPU内存
torch.cuda.empty_cache()

# 设置较小的批处理大小
batch_size = 8  # 根据GPU内存调整
```

## 扩展开发

### 自定义后处理

```python
def custom_postprocess(result):
    """自定义结果后处理"""
    # 过滤低置信度实体
    filtered_entities = [
        entity for entity in result["entities"]
        if len(entity["value"]) > 2  # 过滤太短的实体
    ]
    result["entities"] = filtered_entities
    return result

# 使用自定义后处理
result = unified_inference("测试句子")
result = custom_postprocess(result)
```

### 集成到Web服务

```python
from flask import Flask, request, jsonify
from unified_inference_api import unified_inference

app = Flask(__name__)

@app.route('/inference', methods=['POST'])
def api_inference():
    data = request.json
    sentence = data.get('sentence', '')
    
    if not sentence:
        return jsonify({"error": "句子不能为空"}), 400
    
    try:
        result = unified_inference(sentence)
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

## 故障排除

### 常见问题

**Q: 模型加载失败怎么办？**
A: 检查模型文件路径，确保文件完整。可以先使用模拟模式测试功能。

**Q: 推理结果为空怎么办？**
A: 检查输入句子格式，调整阈值参数，或查看错误日志。

**Q: 内存不足怎么办？**
A: 减少批处理大小，使用CPU推理，或升级硬件。

**Q: 推理速度慢怎么办？**
A: 使用GPU加速，启用批处理，或考虑模型量化。

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
result = unified_inference("测试句子", debug=True)
```

## 更新日志

- **v1.0**: 初始版本，支持基本的统一推理功能
- **v1.1**: 添加批处理支持和错误处理
- **v1.2**: 增加模拟模式和API接口
- **v1.3**: 优化性能和内存使用

## 技术支持

如有问题或建议，请查看：
1. `推理方法分析文档.md` - 技术实现细节
2. 源代码注释 - 详细的函数说明
3. 错误日志 - 具体的错误信息
