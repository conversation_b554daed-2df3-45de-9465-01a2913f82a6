#!/usr/bin/env python3
"""
统一推理脚本 - 结合语法/子域模型和实体/关系模型的推理结果

该脚本可以：
1. 加载两个训练好的双任务模型（语法/子域模型 和 实体/关系模型）
2. 对同一个输入句子运行两个模型的推理
3. 将两个模型的输出合并为一个综合结果

输入格式：单个测试句子（字符串）
输出格式：包含所有四个任务结果的字典
"""

import torch
import os
import sys
import json
from transformers import RobertaTokenizer, BertTokenizer, AutoTokenizer
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入两个模型类
from my_roberta_grammar_subdomainv1 import RoBERTaForConversationClassification as GrammarSubdomainModel
from my_roberta_entity_relationV2 import RoBERTaForConversationClassification as EntityRelationModel

class UnifiedInferenceEngine:
    """
    统一推理引擎 - 结合语法/子域和实体/关系模型
    """
    
    def __init__(self, grammar_model_path=None, entity_model_path=None, tokenizer_path=None):
        """
        初始化统一推理引擎

        Args:
            grammar_model_path: 语法/子域模型的路径
            entity_model_path: 实体/关系模型的路径
            tokenizer_path: 分词器路径（可选，会自动从模型目录查找）
        """
        self.grammar_model = None
        self.entity_model = None
        self.tokenizer = None
        self.grammar_model_path = grammar_model_path
        self.entity_model_path = entity_model_path
        self.tokenizer_path = tokenizer_path

        # 设备检测
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"🔧 使用设备: {self.device}")
        
    def _find_local_tokenizer(self):
        """
        查找本地分词器文件

        Returns:
            str: 分词器路径，如果找不到返回None
        """
        print("🔍 正在查找本地分词器文件...")

        # 候选分词器路径列表
        tokenizer_candidates = []

        # 1. 如果指定了分词器路径，优先使用
        if self.tokenizer_path:
            tokenizer_candidates.append(self.tokenizer_path)

        # 2. 从模型目录中查找分词器
        if self.grammar_model_path and os.path.exists(self.grammar_model_path):
            tokenizer_candidates.append(self.grammar_model_path)

        if self.entity_model_path and os.path.exists(self.entity_model_path):
            tokenizer_candidates.append(self.entity_model_path)

        # 3. 查找其他本地模型目录中的分词器
        local_model_dirs = [
            "./my_models/my_roberta_grammar_subdomain_v1_20250729_021156",
            "./my_models/my_roberta_entity_relation_v1_20250728_071806",
            "./my_models/conversation-roberta",
            "./my_models/my_roberta_V2"
        ]
        tokenizer_candidates.extend(local_model_dirs)

        # 检查每个候选路径
        for candidate in tokenizer_candidates:
            if not candidate:
                continue

            print(f"  - 检查路径: {candidate}")

            # 检查必需的分词器文件
            required_files = ['tokenizer.json', 'tokenizer_config.json']
            optional_files = ['vocab.txt', 'vocab.json', 'merges.txt', 'special_tokens_map.json']

            if os.path.exists(candidate):
                missing_files = []
                for file in required_files:
                    file_path = os.path.join(candidate, file)
                    if not os.path.exists(file_path):
                        missing_files.append(file)

                if not missing_files:
                    print(f"    ✅ 找到完整的分词器文件: {candidate}")
                    return candidate
                else:
                    print(f"    ⚠️  缺少文件: {missing_files}")
            else:
                print(f"    ❌ 路径不存在: {candidate}")

        print("    ❌ 未找到可用的本地分词器")
        return None

    def _load_label_mappings(self):
        """
        加载标签映射文件并设置全局变量
        """
        print("📋 正在加载标签映射文件...")

        # 标签文件路径
        label_files = {
            "grammar": "./datasets/my_roberta_grammar_labels.json",
            "subdomain": "./datasets/my_roberta_subdomain_labels.json",
            "entity": "./datasets/my_roberta_entity_labels.json",
            "relation": "./datasets/my_roberta_relation_labels.json"
        }

        # 导入模型模块以设置全局变量
        import my_roberta_grammar_subdomainv1 as grammar_module
        import my_roberta_entity_relationV2 as entity_module

        try:
            # 使用模型类的load_labels_fromfile方法加载标签
            from my_roberta_grammar_subdomainv1 import RoBERTaForConversationClassification

            # 加载语法标签
            if os.path.exists(label_files["grammar"]):
                grammar_map, grammar_array = RoBERTaForConversationClassification.load_labels_fromfile(label_files["grammar"])
                # 设置全局变量
                grammar_module.CONVERSATION_GRAMMAR_ID_MAP = grammar_map
                grammar_module.CONVERSATION_ID_GRAMMAR_ARY = grammar_array
                print(f"  ✅ 语法标签加载成功: {len(grammar_array)} 个标签")

            # 加载子域标签
            if os.path.exists(label_files["subdomain"]):
                subdomain_map, subdomain_array = RoBERTaForConversationClassification.load_labels_fromfile(label_files["subdomain"])
                grammar_module.CONVERSATION_SUBDOMAIN_ID_MAP = subdomain_map
                grammar_module.CONVERSATION_ID_SUBDOMAIN_ARY = subdomain_array
                print(f"  ✅ 子域标签加载成功: {len(subdomain_array)} 个标签")

            # 加载实体标签
            if os.path.exists(label_files["entity"]):
                entity_map, entity_array = RoBERTaForConversationClassification.load_labels_fromfile(label_files["entity"])
                entity_module.CONVERSATION_ENTITY_ID_MAP = entity_map
                entity_module.CONVERSATION_ID_ENTITY_ARY = entity_array
                print(f"  ✅ 实体标签加载成功: {len(entity_array)} 个标签")

            # 加载关系标签
            if os.path.exists(label_files["relation"]):
                relation_map, relation_array = RoBERTaForConversationClassification.load_labels_fromfile(label_files["relation"])
                entity_module.CONVERSATION_RELATION_ID_MAP = relation_map
                entity_module.CONVERSATION_ID_RELATION_ARY = relation_array
                print(f"  ✅ 关系标签加载成功: {len(relation_array)} 个标签")

        except Exception as e:
            print(f"⚠️  标签映射加载失败: {e}")
            print("💡 将使用默认标签映射")

    def load_models(self):
        """
        加载两个模型和分词器（完全离线模式）
        """
        print("📥 正在加载模型和分词器（离线模式）...")

        # 首先加载标签映射
        self._load_label_mappings()

        try:
            # 查找并加载本地分词器
            tokenizer_path = self._find_local_tokenizer()
            if tokenizer_path:
                print(f"  - 加载本地分词器: {tokenizer_path}")

                # 尝试自动检测分词器类型
                try:
                    # 首先尝试使用AutoTokenizer自动检测
                    self.tokenizer = AutoTokenizer.from_pretrained(
                        tokenizer_path,
                        local_files_only=True
                    )
                    print("    ✅ 分词器加载成功（自动检测类型）")
                except Exception as e1:
                    print(f"    ⚠️  AutoTokenizer失败: {e1}")
                    # 如果AutoTokenizer失败，尝试BertTokenizer
                    try:
                        self.tokenizer = BertTokenizer.from_pretrained(
                            tokenizer_path,
                            local_files_only=True
                        )
                        print("    ✅ 分词器加载成功（BertTokenizer）")
                    except Exception as e2:
                        print(f"    ⚠️  BertTokenizer失败: {e2}")
                        # 最后尝试RobertaTokenizer
                        try:
                            self.tokenizer = RobertaTokenizer.from_pretrained(
                                tokenizer_path,
                                local_files_only=True
                            )
                            print("    ✅ 分词器加载成功（RobertaTokenizer）")
                        except Exception as e3:
                            print(f"    ❌ 所有分词器类型都失败:")
                            print(f"       AutoTokenizer: {e1}")
                            print(f"       BertTokenizer: {e2}")
                            print(f"       RobertaTokenizer: {e3}")
                            raise Exception("无法加载分词器")
            else:
                raise Exception("未找到可用的本地分词器文件")

            # 加载语法/子域模型
            if self.grammar_model_path and os.path.exists(self.grammar_model_path):
                print(f"  - 加载语法/子域模型: {self.grammar_model_path}")
                self.grammar_model = GrammarSubdomainModel.from_pretrained(
                    self.grammar_model_path,
                    local_files_only=True
                )
                self.grammar_model.to(self.device)
                self.grammar_model.eval()
                print("    ✅ 语法/子域模型加载成功")
            else:
                print(f"    ⚠️  语法/子域模型路径无效或不存在: {self.grammar_model_path}")

            # 加载实体/关系模型
            if self.entity_model_path and os.path.exists(self.entity_model_path):
                print(f"  - 加载实体/关系模型: {self.entity_model_path}")
                self.entity_model = EntityRelationModel.from_pretrained(
                    self.entity_model_path,
                    local_files_only=True
                )
                self.entity_model.to(self.device)
                self.entity_model.eval()
                print("    ✅ 实体/关系模型加载成功")
            else:
                print(f"    ⚠️  实体/关系模型路径无效或不存在: {self.entity_model_path}")

        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            print("💡 提示: 请确保模型文件和分词器文件都在本地目录中")
            print("💡 检查以下目录是否包含完整的模型文件:")
            print("   - ./my_models/my_roberta_grammar_subdomain_v1_20250729_021156/")
            print("   - ./my_models/my_roberta_entity_relation_v1_20250728_071806/")
            raise
            
    def run_grammar_subdomain_inference(self, sentence, grammar_threshold=0.5):
        """
        运行语法/子域推理
        
        Args:
            sentence: 输入句子
            grammar_threshold: 语法分类阈值
            
        Returns:
            dict: 语法和子域预测结果
        """
        if self.grammar_model is None:
            return {"grammar": [], "subdomain": "未知", "error": "语法/子域模型未加载"}
            
        try:
            results = self.grammar_model.inference(
                sentences=[sentence],
                tokenizer=self.tokenizer,
                grammar_threshold=grammar_threshold
            )
            return {
                "grammar": results[0]["grammar"],
                "subdomain": results[0]["subdomain"]
            }
        except Exception as e:
            return {"grammar": [], "subdomain": "未知", "error": f"语法/子域推理失败: {e}"}
    
    def run_entity_relation_inference(self, sentence, start_threshold=0.5, end_threshold=0.5):
        """
        运行实体/关系推理
        
        Args:
            sentence: 输入句子
            start_threshold: 实体开始位置阈值
            end_threshold: 实体结束位置阈值
            
        Returns:
            dict: 实体和关系预测结果
        """
        if self.entity_model is None:
            return {"entities": [], "relations": [], "error": "实体/关系模型未加载"}
            
        try:
            results = self.entity_model.inference(
                sentences=[sentence],
                tokenizer=self.tokenizer,
                start_threshold=start_threshold,
                end_threshold=end_threshold
            )
            return {
                "entities": results[0]["entities"],
                "relations": results[0]["relations"]
            }
        except Exception as e:
            return {"entities": [], "relations": [], "error": f"实体/关系推理失败: {e}"}
    
    def unified_inference(self, sentence, grammar_threshold=0.5, start_threshold=0.5, end_threshold=0.5):
        """
        统一推理 - 结合两个模型的结果
        
        Args:
            sentence: 输入句子
            grammar_threshold: 语法分类阈值
            start_threshold: 实体开始位置阈值
            end_threshold: 实体结束位置阈值
            
        Returns:
            dict: 包含所有四个任务结果的综合字典
        """
        print(f"🔍 正在对句子进行统一推理: '{sentence}'")
        
        # 运行语法/子域推理
        print("  - 运行语法/子域推理...")
        grammar_results = self.run_grammar_subdomain_inference(sentence, grammar_threshold)
        
        # 运行实体/关系推理
        print("  - 运行实体/关系推理...")
        entity_results = self.run_entity_relation_inference(sentence, start_threshold, end_threshold)
        
        # 合并结果
        unified_result = {
            "sentence": sentence,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "grammar": grammar_results.get("grammar", []),
            "subdomain": grammar_results.get("subdomain", "未知"),
            "entities": entity_results.get("entities", []),
            "relations": entity_results.get("relations", []),
            "model_status": {
                "grammar_subdomain_model": "已加载" if self.grammar_model else "未加载",
                "entity_relation_model": "已加载" if self.entity_model else "未加载"
            }
        }
        
        # 添加错误信息（如果有）
        errors = []
        if "error" in grammar_results:
            errors.append(f"语法/子域: {grammar_results['error']}")
        if "error" in entity_results:
            errors.append(f"实体/关系: {entity_results['error']}")
        if errors:
            unified_result["errors"] = errors
            
        return unified_result
    
    def print_result(self, result):
        """
        格式化打印推理结果
        
        Args:
            result: unified_inference返回的结果字典
        """
        print("\n" + "="*80)
        print("📊 统一推理结果")
        print("="*80)
        print(f"📝 输入句子: {result['sentence']}")
        print(f"🕒 推理时间: {result['timestamp']}")
        print()
        
        print("🎯 语法分类结果:")
        if result['grammar']:
            for grammar in result['grammar']:
                print(f"  - {grammar}")
        else:
            print("  - 无语法类型识别")
        print()
        
        print("🏷️  子域分类结果:")
        print(f"  - {result['subdomain']}")
        print()
        
        print("🔍 实体识别结果:")
        if result['entities']:
            for i, entity in enumerate(result['entities'], 1):
                print(f"  {i}. 类型: {entity['type']}, 值: '{entity['value']}', 位置: {entity['start']}-{entity['end']}")
        else:
            print("  - 无实体识别")
        print()
        
        print("🔗 关系抽取结果:")
        if result['relations']:
            for i, relation in enumerate(result['relations'], 1):
                print(f"  {i}. {relation['entity1']['value']} --[{relation['relation_type']}]--> {relation['entity2']['value']}")
        else:
            print("  - 无关系识别")
        print()
        
        print("⚙️  模型状态:")
        for model, status in result['model_status'].items():
            print(f"  - {model}: {status}")
        
        if 'errors' in result:
            print("\n⚠️  错误信息:")
            for error in result['errors']:
                print(f"  - {error}")
        
        print("="*80)

def test_with_sample_data():
    """
    使用示例数据测试统一推理功能（无需加载实际模型）
    """
    print("🧪 运行示例测试（模拟推理结果）")
    print("="*60)

    # 创建模拟的推理引擎
    engine = UnifiedInferenceEngine()

    # 模拟推理结果
    test_sentence = "什么是机器学习的定义？"

    mock_result = {
        "sentence": test_sentence,
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "grammar": ["Query", "Definition"],
        "subdomain": "AI/ML",
        "entities": [
            {"start": 2, "end": 4, "type": "Concept", "value": "机器学习"},
            {"start": 5, "end": 6, "type": "Property", "value": "定义"}
        ],
        "relations": [
            {
                "entity1": {"start": 2, "end": 4, "type": "Concept", "value": "机器学习"},
                "entity2": {"start": 5, "end": 6, "type": "Property", "value": "定义"},
                "relation_type": "Has_Property"
            }
        ],
        "model_status": {
            "grammar_subdomain_model": "模拟模式",
            "entity_relation_model": "模拟模式"
        }
    }

    print(f"📝 测试句子: {test_sentence}")
    engine.print_result(mock_result)

    return mock_result

def main():
    """
    主函数 - 交互式推理界面
    """
    print("🚀 统一推理引擎启动")
    print("="*60)

    # 提供选择：实际推理或示例测试
    print("请选择运行模式:")
    print("1. 实际模型推理（需要训练好的模型文件）")
    print("2. 示例测试（使用模拟数据）")

    try:
        choice = input("请输入选择 (1/2): ").strip()

        if choice == "2":
            test_with_sample_data()
            return
        elif choice != "1":
            print("无效选择，使用示例测试模式")
            test_with_sample_data()
            return
    except:
        print("使用示例测试模式")
        test_with_sample_data()
        return

    # 实际模型推理模式
    engine = UnifiedInferenceEngine()

    # 尝试自动查找模型文件
    print("🔍 正在查找模型文件...")

    # 查找语法/子域模型
    grammar_model_candidates = [
        "./my_models/my_roberta_grammar_subdomain_v1_20250729_021156",
    ]

    for path in grammar_model_candidates:
        if os.path.exists(path):
            engine.grammar_model_path = path
            print(f"  ✅ 找到语法/子域模型: {path}")
            break

    # 查找实体/关系模型
    entity_model_candidates = [
        "./my_models/my_roberta_entity_relation_v1_20250728_071806",
    ]

    for path in entity_model_candidates:
        if os.path.exists(path):
            engine.entity_model_path = path
            print(f"  ✅ 找到实体/关系模型: {path}")
            break

    # 如果没有找到模型，提示用户
    if not engine.grammar_model_path:
        print("  ⚠️  未找到语法/子域模型，请手动指定路径")
    if not engine.entity_model_path:
        print("  ⚠️  未找到实体/关系模型，请手动指定路径")

    # 加载模型
    try:
        engine.load_models()
    except Exception as e:
        print(f"❌ 模型加载失败，切换到示例测试模式: {e}")
        test_with_sample_data()
        return

    print("\n🎉 统一推理引擎准备就绪！")
    print("💡 输入测试句子进行推理，输入 'quit' 退出程序")
    print("-"*60)

    # 交互式推理循环
    while True:
        try:
            sentence = input("\n📝 请输入测试句子: ").strip()

            if sentence.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 再见！")
                break

            if not sentence:
                print("⚠️  请输入有效的句子")
                continue

            # 运行统一推理
            result = engine.unified_inference(sentence)

            # 打印结果
            engine.print_result(result)

        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 推理过程中发生错误: {e}")

if __name__ == "__main__":
    main()
